import '../support/commands.ts';

describe('PCW E2E test: Success/danger alerts', () => {
  beforeEach(() => {
    // Login to Keycloak
    cy.loginWithKeycloak();
    // Cypress starts out with a blank slate for each test
    // so we must tell it to visit our website with the `cy.visit()` command.
    cy.visit(Cypress.env('base_url') + '/pcw/landing');
  });

  afterEach(() => {
    //cy.logoutWithKeycloak();
  });

  const config = {
    connectionString: Cypress.env('connectionString'),
    user: Cypress.env('user'),
    password: Cypress.env('password')
  };

  const inputProductLineID = 'G2';
  const inputProductLineIdVersion = '1';

  it('Success alerts', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();

    //create New UI config
    cy.get('[data-testid="add-new-ui-config-button"]').click();
    cy.wait(1000);
    cy.get('[data-testid="ui-config-name-input"] input').type('alerts', { force: true });
    cy.get('[data-testid="ui-config-description-input"]').eq(0).type('test description');
    cy.get('[data-testid="ui-config-object-type-select"]').click();
    cy.get('[data-value="G2"]').click();
    cy.get('[data-testid="save-button"]').first().click();
    //cy.get('[data-testid="ui-config-success-alert"]').should('be.visible').contains('Changes were saved.');
    //cy.contains('.MuiTypography-root.MuiTypography-medium', 'Success Changes were saved.').should('be.visible');
    //verify success alert
    cy.verifySuccessMessage();
    cy.wait(5000);
    cy.get('[data-testid="risk-and-objects-tab"]').click();
    cy.get('[data-testid="ui-configuration-tab"]').click();
    cy.filterUIConfig('alerts');
    //create new UI object block
    cy.get('[data-testid="new-block-button"]').eq(0).click();
    cy.get('[data-testid="block-name-input"]').type('test name1');
    cy.get('[data-testid="block-description-input"]').eq(0).type('test description1');
    cy.get('[data-testid="block-expanded-checkbox"] > :first-child').eq(0).check({ force: true });
    cy.get('[data-testid="block-read-only-checkbox"] > :first-child').eq(0).check({ force: true });
    cy.get('[data-testid="block-sort-no-input"]').eq(0).type('7');
    cy.get('[data-testid="save-button"]').first().click();
    //cy.get('[data-testid="ui-config-success-alert"]').should('be.visible').contains('Changes were saved.');
    // cy.contains('.MuiTypography-root.MuiTypography-medium', 'Success Changes were saved.').should('be.visible');
    //verify success alert
    cy.verifySuccessMessage();
    cy.wait(5000);
    cy.get('[data-testid="risk-and-objects-tab"]').click();
    cy.get('[data-testid="ui-configuration-tab"]').click();
    // navigate to object
    cy.filterUIConfig('alerts');
    cy.get('[data-testid="block-name-cell-0"] a').first().click();
    // Verify that the button with text "Add field" exists
    cy.contains('button', 'Add field').should('exist');
    //Click on Add field button
    cy.clickButtonWithText('Add field');
    cy.wait(1000);
    //select Column name
    cy.selectColumnName('BONUS C07');
    // enter value Order field
    cy.enterOrderValue('1');
    // Choose Length
    cy.selectOptionFromDropdown('Large');
    cy.saveUIConfig();
    cy.wait(5000);
    //verify success alert
    cy.verifySuccessMessage();

    // Find the link with the text "Product Line Details" and click it
    cy.contains('a', 'Product Line Details').click();
    // Click on UI config tab
    cy.get('[data-testid="ui-configuration-tab"]').click();
    // navigate to object
    cy.filterUIConfig('alerts');
    cy.get('[data-testid="block-name-cell-0"] a').first().click();
    cy.wait(5000); // Adjust the wait time if necessary

    //delete Object UI config field
    cy.get('[data-testid="DeleteIcon"]').click();
    // Click on the button with the test ID "delete-modal-confirm-button"
    cy.get('[data-testid="delete-modal-confirm-button"]').click();
    //verify success alert
    cy.verifyDeleteSuccessMessage();
    cy.wait(10000);
    //verify  Add field for configuration is displayed
    cy.contains('p', 'Add fields for configuration').should('be.visible');
    // Find the link with the text "Product Line Details" and click it
    cy.contains('a', 'Product Line Details').click();
    // Click on UI config tab
    cy.get('[data-testid="ui-configuration-tab"]').click();
    // navigate to object
    cy.filterUIConfig('alerts');
    //delete UI config
    cy.get('[data-testid="DeleteIcon"]').eq(0).click();
    cy.get('[data-testid="delete-modal-confirm-button"]').click();
    //verify success alert
    cy.verifyDeleteSuccessMessage();
  });

  it('Danger alerts', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();
    cy.filterUIConfig('ADF_G2');
    // Open Version dropdown
    cy.get('[data-testid="ui-config-version-select"]').click();
    // Select the first available option
    cy.get('ul[role="listbox"] li').first().click();
    // Open Version dropdown again and select version 1
    cy.get('[data-testid="ui-config-version-select"]').click();
    cy.get('ul[role="listbox"] li').eq(1).click();

    // Check that error msg is shown
    cy.get('.Toastify__toast--error')
      .should('be.visible')
      .within(() => {
        cy.get('.MuiTypography-root').should(
          'contain',
          'Another UI Configuration version can be set only after all changes for the version in focus are saved or discarded. Please save or revert pending changes and then select another UI Configuration version.'
        );
      });
  });
});
