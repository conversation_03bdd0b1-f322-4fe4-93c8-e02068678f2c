path: /v1/ui-configuration-risks-with-fields/{uiConfigurationRiskId}
method: delete
flow: pcwDeleteUiConfigurationRisksWithFields.yaml
secured: false
errorHandlers:
  - PCW_UNAUTHORIZED.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_BAD_REQUEST.yaml
definition:
  operationId: pcwDeleteUiConfigurationRisksWithFields
  tags:
    - pcw
  summary: Delete UI Configuration risks with fields
  description: ""
  parameters:
    - in: path
      name: uiConfigurationRiskId
      required: true
      description: ""
      schema:
        type: number
  responses: {}
