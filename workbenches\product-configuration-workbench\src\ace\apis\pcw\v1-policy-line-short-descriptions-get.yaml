path: /v1/policy-line-short-descriptions
method: get
flow: pcwGetPolicyLineShortDescriptions.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_UNAUTHORIZED.yaml
  - PCW_NOT_FOUND.yaml
definition:
  operationId: pcwGetPolicyLineShortDescriptions
  tags:
    - pcw
  summary: Get list of policy line short description attributes by input parameters.
  description: ""
  parameters:
    - in: query
      name: productLineId
      required: false
      description: Product line identification
      schema:
        type: string
    - in: query
      name: productLineVerNo
      required: false
      description: Product line version
      schema:
        type: string
    - in: query
      name: columnName
      required: false
      description: Column name. Partial text search
      schema:
        type: string
    - in: query
      name: attributeName
      required: false
      description: Attribute name. Partial text search
      schema:
        type: string
    - in: query
      name: page
      required: false
      description: Result page you want to retrieve (1..N)
      schema:
        type: string
    - in: query
      name: size
      required: false
      description: Number of records per page
      schema:
        type: string
  responses: {}
