path: /v1/recent-products
method: get
flow: pcwGetRecentProducts.yaml
secured: false
errorHandlers:
  - PCW_UNAUTHORIZED.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_BAD_REQUEST.yaml
definition:
  operationId: pcwGetRecentProducts
  tags:
    - pcw
  summary: Fetches recent products from Tia
  description: ""
  parameters:
    - in: query
      name: page
      required: false
      description: Page number
      schema:
        type: number
    - in: query
      name: size
      required: false
      description: Page size
      schema:
        type: number
    - in: query
      name: sort
      required: false
      description: "Sort parameter. Format: property(:asc|desc)."
      schema:
        type: string
    - in: query
      name: recordUserid
      required: false
      description: Filter by record user id
      schema:
        type: string
    - in: query
      name: language
      required: false
      description: In which language data should be fetched
      schema:
        type: string
  responses: {}
