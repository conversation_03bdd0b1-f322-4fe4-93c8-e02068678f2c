path: /v1/ui-configurations/{uiConfigurationId}
method: delete
flow: pcwDeleteProductLineUiConfigurations.yaml
secured: false
errorHandlers:
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
  - PCW_BAD_REQUEST.yaml
definition:
  operationId: pcwDeleteProductLineUiConfigurations
  tags:
    - pcw
  summary: Delete UI Configurations
  description: ""
  parameters:
    - in: path
      name: uiConfigurationId
      required: true
      description: UI Configuration List UIX_SEQ_NO.
      schema:
        type: number
    - in: query
      name: cascade
      required: false
      description: Cascade delete? If TRUE, all underlying UI configuration components
        will be deleted. Default value is FALSE.
      schema:
        type: boolean
  responses: {}
