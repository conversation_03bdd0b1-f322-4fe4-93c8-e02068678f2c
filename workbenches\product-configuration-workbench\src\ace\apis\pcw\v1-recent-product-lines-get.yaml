path: /v1/recent-product-lines
method: get
flow: pcwGetRecentProductLines.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_UNAUTHORIZED.yaml
  - PCW_NOT_FOUND.yaml
definition:
  operationId: pcwGetRecentProductLines
  tags:
    - pcw
  summary: Fetches recent product lines from TIA
  description: ""
  parameters:
    - in: query
      name: page
      required: false
      description: Page number
      schema:
        type: number
    - in: query
      name: size
      required: false
      description: Page size
      schema:
        type: number
    - in: query
      name: sort
      required: false
      description: "Sort parameter. Format: property(:asc|desc)."
      schema:
        type: string
    - in: query
      name: recordUserid
      required: false
      description: Filter by record user id
      schema:
        type: string
    - in: query
      name: language
      required: false
      description: In which language data should be fetched
      schema:
        type: string
  responses: {}
