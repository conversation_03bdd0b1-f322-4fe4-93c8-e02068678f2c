import { ApiResponseList, axios } from '@sapiens/workbench-feature-library';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { Workpads } from '@/models/workpads.model';

export const FSW_WORKPAD_BASE_URL = 'fsw/v1/workpads';

export type WorkpadParams = {
  workpadTitle?: string;
  workpadType?: number;
  page: number;
  size: number;
};

const getWorkpads = async (params: WorkpadParams): Promise<ApiResponseList<Workpads>> => {
  const response = await axios.get(`/fsw/v1/workpads`, { params });
  return response.data;
};

export const useGetWorkpads = (params: WorkpadParams) => {
  return useQuery({
    queryKey: ['workpads', { ...params }],
    queryFn: async () => getWorkpads(params),
    keepPreviousData: true,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    enabled: true
  });
};

export type CreateWorkpadPayload = {
  workpadType: number;
  workpadTitle: string;
};

export const createWorkpad = async (payload: CreateWorkpadPayload): Promise<void> => {
  await axios.post(`/${FSW_WORKPAD_BASE_URL}`, payload);
};

export const useCreateWorkpad = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: CreateWorkpadPayload) => createWorkpad(payload),
    onSuccess: () => {
      queryClient.invalidateQueries(['workpads']);
    }
  });
};

export type UpdateWorkpadPayload = {
  workpadNo: number;
  workpadType: number;
  workpadTitle: string;
};

export const updateWorkpad = async (payload: UpdateWorkpadPayload): Promise<void> => {
  await axios.put(`/${FSW_WORKPAD_BASE_URL}/${payload.workpadNo}`, {
    workpadType: payload.workpadType,
    workpadTitle: payload.workpadTitle
  });
};

export const useUpdateWorkpad = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: UpdateWorkpadPayload) => updateWorkpad(payload),
    onSuccess: () => {
      queryClient.invalidateQueries(['workpads']);
    }
  });
};
