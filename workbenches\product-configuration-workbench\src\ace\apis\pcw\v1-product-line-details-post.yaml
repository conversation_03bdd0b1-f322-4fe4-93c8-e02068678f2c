path: /v1/product-line-details
method: post
flow: pcwPostProductLineDetails.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_UNAUTHORIZED.yaml
definition:
  operationId: pcwPostProductLineDetails
  tags:
    - pcw
  summary: ""
  description: ""
  parameters: []
  requestBody:
    description: ""
    content:
      application/json:
        schema:
          $ref: pcwInsertProductLineDetailRequest.yaml
  responses: {}
