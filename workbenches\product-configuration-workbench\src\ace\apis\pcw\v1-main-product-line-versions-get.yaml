path: /v1/main-product-line-versions
method: get
flow: pcwGetMainProductLineVersions.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
definition:
  operationId: pcwGetMainProductLineVersions
  tags:
    - pcw
  summary: Fetches main product line versions from Tia
  description: ""
  parameters:
    - in: query
      name: productLineId
      required: false
      description: Main product line identification. Partial text search.
      schema:
        type: string
    - in: query
      name: description
      required: false
      description: Main product line description. Partial text search.
      schema:
        type: string
    - in: query
      name: page
      required: false
      description: Result page you want to retrieve (1..N)
      schema:
        type: number
    - in: query
      name: size
      required: false
      description: Number of records per page
      schema:
        type: number
  responses: {}
