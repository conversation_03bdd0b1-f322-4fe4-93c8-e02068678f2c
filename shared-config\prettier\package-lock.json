{"name": "@shared-config/prettier", "version": "1.0.0", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "@shared-config/prettier", "version": "1.0.0", "dependencies": {"@shared-config/prettier": "file:", "prettier": "^3.0.0"}}, "node_modules/@shared-config/prettier": {"resolved": "", "link": true}, "node_modules/prettier": {"version": "3.1.0", "license": "MIT", "bin": {"prettier": "bin/prettier.cjs"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}}, "dependencies": {"@shared-config/prettier": {"version": "file:", "requires": {"@shared-config/prettier": "file:", "prettier": "^3.0.0"}, "dependencies": {"prettier": {"version": "3.1.0"}}}, "prettier": {"version": "3.1.0"}}}