path: /v1/product-line-entity-versions
method: get
flow: pcwGetProductLineEntityVersions.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
definition:
  operationId: pcwGetProductLineEntityVersions
  tags:
    - pcw
  summary: Fetches linked product line entity versions from TIA
  description: ""
  parameters:
    - in: query
      name: page
      required: false
      description: Page number
      schema:
        type: number
    - in: query
      name: size
      required: false
      description: Page size
      schema:
        type: number
    - in: query
      name: language
      required: false
      description: In which language data should be fetched
      schema:
        type: string
  responses: {}
