import {
  DataTableOptions,
  DynamicType,
  generateFilterParams,
  toSortParams,
  useDataTable,
  useDiscardChanges,
  useTableQuery
} from '@sapiens/workbench-feature-library';
import { ColumnDef, ColumnFiltersState, PaginationState, SortingState, Updater } from '@tanstack/react-table';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';

import { TableResetContextProps } from './TableFormProvider';

export interface AdditionalDataTableOptions {
  errorText: string;
  noRecordsText: React.ReactNode;
  noRecordsHeader?: string;
}

export type ServerSideEditTableOptions = DataTableOptions & Partial<AdditionalDataTableOptions>;

interface UseServerSideEditTableProps<T> {
  /**
   * API endpoint URL for fetching table data.
   */
  url: string;
  /**
   * Column definitions for the table.
   */
  columns: ColumnDef<T>[];
  /**
   * Configuration options for server-side editing.
   */
  options: Partial<ServerSideEditTableOptions>;
  /**
   * Unique identifier for the table.
   */
  tableId: string;
  /**
   * Callback invoked when edits are discarded.
   */
  discardCallback: () => void;
  /**
   * Configuration for adding new rows.
   */
  addNewRowConfig?: {
    newButtonLabel: string;
    callback: () => void;
    disabled: boolean;
  };
}

const initialTableResetContext = {
  resetCellState: {
    trigger: false,
    allow: false
  }
};

/**
 * Hook to manage server-side editing functionalities.
 *
 * @template T - The type of data represented in the table.
 * @param props - Props containing API URL, columns, options, and callbacks.
 * @returns An object containing table columns, component props, data, context, and methods.
 */
export const useServerSideEditTable = <T extends object>({
  url,
  columns,
  options,
  tableId,
  discardCallback,
  addNewRowConfig
}: UseServerSideEditTableProps<T>) => {
  const [context, setContext] = useState<TableResetContextProps>(initialTableResetContext);
  const [isFieldsLoading, setIsFieldsLoading] = useState(true);
  const {
    errorText,
    noRecordsText,
    noRecordsHeader,
    toolbarSlot,
    additionalFilters,
    isPageSizeSelectable,
    pageSizeOptions,
    ...restOptions
  } = options;
  const { hasUnsavedChanges, handleUnsavedChanges, promptDiscardConfirmation } = useDiscardChanges();

  const { tableProps } = useDataTable({ columnOrder: columns.map((column) => column.id as string), ...restOptions });
  const {
    columnFilters,
    sorting,
    pagination,
    setPagination,
    setSorting,
    setColumnFilters,
    setRowSelection,
    defaultPageSize,
    searchWithAdditionalFilters
  } = tableProps;

  const handleUpdateWithChanges = useCallback(
    async <T>(updaterFn: React.Dispatch<React.SetStateAction<T>>, updater: Updater<T>) => {
      if (!hasUnsavedChanges(tableId)) {
        updaterFn(updater);
        discardCallback?.();
        return;
      }
      const confirmed = await promptDiscardConfirmation([tableId]);
      if (confirmed) {
        updaterFn(updater);
      }
    },
    [hasUnsavedChanges, promptDiscardConfirmation, tableId]
  );

  const updateSorting = useCallback(
    (updater: Updater<SortingState>) => handleUpdateWithChanges(setSorting, updater),
    [setSorting, handleUpdateWithChanges]
  );

  const updatePagination = useCallback(
    (updater: Updater<PaginationState>) => handleUpdateWithChanges(setPagination, updater),
    [setPagination, handleUpdateWithChanges]
  );

  const updateColumnFilters = useCallback(
    (updater: Updater<ColumnFiltersState>) => handleUpdateWithChanges(setColumnFilters, updater),
    [setColumnFilters, handleUpdateWithChanges]
  );

  const generatedFilters = useMemo(() => {
    return generateFilterParams(columnFilters, searchWithAdditionalFilters, additionalFilters);
  }, [columnFilters, searchWithAdditionalFilters, additionalFilters]);

  const params = useMemo(() => {
    return {
      page: pagination.pageIndex,
      size: pagination.pageSize,
      sort: toSortParams(sorting),
      filters: generatedFilters
    };
  }, [pagination.pageIndex, pagination.pageSize, sorting, columnFilters, additionalFilters, generatedFilters]);

  const { data, isLoading, fetchStatus, isError, error, isFetchedAfterMount } = useTableQuery<T>(url, params);

  const methods = useForm({
    defaultValues: {
      data: data?.content as DynamicType
    }
  });

  const {
    control,
    reset,
    formState: { isDirty }
  } = methods;
  const { fields } = useFieldArray({
    control,
    name: 'data',
    keyName: 'uuid'
  });

  useEffect(() => {
    if (data?.content && isFetchedAfterMount) {
      reset({ data: data.content });
      setIsFieldsLoading(false);
    }
  }, [data?.content, reset, isFetchedAfterMount]);

  useEffect(() => {
    if (isDirty) {
      handleUnsavedChanges(tableId, true);
    }
  }, [isDirty, handleUnsavedChanges, tableId]);

  const addNewRowCallback = useCallback(() => {
    addNewRowConfig?.callback();
  }, [addNewRowConfig]);

  const pageSize = isPageSizeSelectable ? defaultPageSize : pagination.pageSize;

  const tableStatusIndicatorProps = useMemo(() => {
    return {
      isLoading: isLoading || isFieldsLoading,
      isError,
      errorText,
      noRecordsText,
      noRecordsHeader
    };
  }, [isLoading, isFieldsLoading, isError, errorText, noRecordsText, noRecordsHeader]);

  const paginationProps = useMemo(() => {
    return {
      totalRecords: data?.page?.totalElements,
      isPageSizeSelectable,
      pageSizeOptions
    };
  }, [data?.page?.totalElements, isPageSizeSelectable, pageSizeOptions]);

  const componentProps = useMemo(() => {
    const defaultAddRowConfig = addNewRowConfig
      ? {
          newButtonLabel: addNewRowConfig?.newButtonLabel,
          disabled: addNewRowConfig.disabled,
          callback: addNewRowCallback
        }
      : undefined;

    return {
      ...tableProps,
      tableStatusIndicators: tableStatusIndicatorProps,
      paginationProps,
      toolbarSlot,
      setSorting: updateSorting,
      setPagination: updatePagination,
      setColumnFilters: updateColumnFilters,
      showPagination: options.showPagination ?? (data?.page?.totalElements as number) > pageSize,
      addNewRowConfig: defaultAddRowConfig
    };
  }, [
    tableProps,
    tableStatusIndicatorProps,
    paginationProps,
    toolbarSlot,
    updateSorting,
    updatePagination,
    updateColumnFilters,
    options.showPagination,
    data?.page?.totalElements,
    pageSize,
    addNewRowCallback
  ]);

  return {
    columns,
    componentProps,
    data: fields,
    context,
    setContext,
    methods
  };
};
