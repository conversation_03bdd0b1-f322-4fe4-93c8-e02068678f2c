path: /v1/ui-configuration-risks-with-fields/{uiConfigurationRiskId}
method: put
flow: pcwUpdateUIConfigurationRisksWithFields.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
definition:
  operationId: pcwUpdateUIConfigurationRisksWithFields
  tags:
    - pcw
  summary: ""
  description: ""
  parameters:
    - in: path
      name: uiConfigurationRiskId
      required: true
      description: ""
      schema:
        type: number
  requestBody:
    content:
      application/json: {}
  responses: {}
