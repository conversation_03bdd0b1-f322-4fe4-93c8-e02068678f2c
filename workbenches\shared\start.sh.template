#!/bin/sh

serverConfiguration="listen ${HTTP_PORT:=8080};"

if [[ "${SSL_ENABLED}" = "true" && -n "${SSL_SERVER_NAME}" && -n "${SSL_CERTIFICATE}" && -n "${SSL_CERTIFICATE_KEY}" ]]; then
    if [[ "${ENABLE_HTTP_PORT}" != "true" ]]; then
      serverConfiguration=""
    fi
    serverConfiguration+="\n  listen ${HTTPS_PORT:=8443} ssl;"
    serverConfiguration+="\n  server_name ${SSL_SERVER_NAME};"
    serverConfiguration+="\n  ssl_certificate ${SSL_CERTIFICATE};"
    serverConfiguration+="\n  ssl_certificate_key ${SSL_CERTIFICATE_KEY};"
fi

sed -i "s|%serverConfiguration%|${serverConfiguration}|g" /etc/nginx/conf.d/default.conf

contextPath=${CONTEXT_PATH:="/%workbenchShortName%"}
clientHeaderBufferSize=${CLIENT_HEADER_BUFFER_SIZE:="16k"}
workbenchVersion=${WORKBENCH_VERSION:="unknown"}

mkdir -p /usr/share/nginx/html/${contextPath}
cp -RT /app-src /usr/share/nginx/html/${contextPath}

# configuring the context path
for file in $(grep -rl '/__CONTEXT_PATH__' /usr/share/nginx/html); do
  sed -i "s|/__CONTEXT_PATH__|${contextPath}|g" "$file"
done

sed -i "s|%contextPath%|${contextPath}|g" /etc/nginx/conf.d/default.conf
sed -i "s|%clientHeaderBufferSize%|${clientHeaderBufferSize}|g" /etc/nginx/conf.d/default.conf
sed -i "s|%workbenchVersion%|${workbenchVersion}|g" /etc/nginx/conf.d/default.conf

cd /usr/share/nginx/html/${contextPath} || exit

sed -i "s|%contextPath%|${contextPath}|g" environment/environment.json
sed -i "s|%gatewayBaseUrl%|${GATEWAY_BASE_URL}|g" environment/environment.json
sed -i "s|%authDomain%|${AUTH_DOMAIN}|g" environment/environment.json
sed -i "s|%authTokenDomain%|${AUTH_TOKEN_DOMAIN}|g" environment/environment.json
sed -i "s|%authLogoutDomain%|${AUTH_LOGOUT_DOMAIN}|g" environment/environment.json
sed -i "s|%authUserInfoDomain%|${AUTH_USER_INFO_DOMAIN}|g" environment/environment.json
sed -i "s|%authLoadUserInfo%|${AUTH_LOAD_USER_INFO:="true"}|g" environment/environment.json
sed -i "s|%authAudience%|${AUTH_AUDIENCE}|g" environment/environment.json
sed -i "s|%authClientId%|${AUTH_CLIENT_ID}|g" environment/environment.json
sed -i "s|%authRoleClaim%|${AUTH_ROLE_CLAIM}|g" environment/environment.json
sed -i "s|%authScope%|${AUTH_SCOPE}|g" environment/environment.json
sed -i "s|%isSPDEnabled%|${IS_SPD_ENABLED:="false"}|g" environment/environment.json
sed -i "s|%isElasticSearchEnabled%|${ELASTIC_SEARCH_ENABLED:="true"}|g" environment/environment.json
sed -i "s|%searchTriggerLength%|${SEARCH_TRIGGER_LENGTH:="2"}|g" environment/environment.json
sed -i "s|%inputDateFormat%|${INPUT_DATE_FORMAT:="##-##-####"}|g" environment/environment.json
sed -i "s|%dateFormat%|${DATE_FORMAT:="DD-MM-YYYY"}|g" environment/environment.json
sed -i "s|%dateTimeFormat%|${DATE_TIME_FORMAT:="DD-MM-YYYY HH:mm"}|g" environment/environment.json
sed -i "s|%dateTimezone%|${DATE_TIMEZONE}|g" environment/environment.json
sed -i "s|%alternativeDateFormat%|${ALTERNATIVE_DATE_FORMAT:="YYYY-MM-DD"}|g" environment/environment.json
sed -i "s|%backendDateFormat%|${BACK_END_DATE_FORMAT:="YYYY-MM-DD[T]HH:mm:ss.SSS"}|g" environment/environment.json
sed -i "s|%dateFormatOrder%|${DATE_FORMAT_ORDER:="D,D,M,M,Y,Y,Y,Y"}|g" environment/environment.json
sed -i "s|%postStreetEnabled%|${POST_STREET_ENABLED:="false"}|g" environment/environment.json
sed -i "s|%nationalIdFormat%|${NATIONAL_ID_FORMAT:="######-####"}|g" environment/environment.json
sed -i "s|%accessTokenRenewalTimeBeforeExpiration%|${ACCESS_TOKEN_RENEWAL_TIME_BEFORE_EXPIRATION:="60"}|g" environment/environment.json
sed -i "s|%spdSalesUiEndpoint%|${SPD_SALES_UI_ENDPOINT}|g" environment/environment.json
sed -i "s|%allowDocumentDownload%|${ALLOW_DOCUMENT_DOWNLOAD}|g" environment/environment.json
sed -i "s|%widgetsDynamicUrls%|${WIDGETS_DYNAMIC_URLS}|g" environment/environment.json
sed -i "s|%userProfileIdentities%|${USER_PROFILE_IDENTITIES}|g" environment/environment.json
sed -i "s|%defaultPoolTaskTabFlag%|${DEFAULT_POOL_TASK_TAB_FLAG}|g" environment/environment.json

sed -i "s|%tiaADFDeepLinkUrl%|${TIA_ADF_DEEPLINK_URL}|g" environment/environment.json
sed -i "s|%tiaYesNoReferenceNaming%|${TIA_YES_NO_REFERENCE_NAMING:="JANEE"}|g" environment/environment.json
sed -i "s|%tiaProductReferenceGenericId%|${TIA_PRODUCT_REFERENCE_GENERIC_ID}|g" environment/environment.json

sed -i "s|%otelTracesServiceName%|${OTEL_TRACES_SERVICE_NAME}|g" environment/environment.json
sed -i "s|%otelTracesCollectorEndpoint%|${OTEL_TRACES_COLLECTOR_ENDPOINT}|g" environment/environment.json
sed -i "s|%otelTracesHeaderCorsUrls%|${OTEL_TRACES_HEADER_CORS_URLS}|g" environment/environment.json
sed -i "s|%otelEnableinstrumentation%|${OTEL_ENABLE_INSTRUMENTATION:="none"}|g" environment/environment.json

%placeholderForAppSpecificConfiguration%


echo Using configuration:
cat environment/environment.json

nginx -g 'daemon off;'
