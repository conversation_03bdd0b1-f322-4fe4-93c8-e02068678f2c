path: /v1/product-line-details
method: get
flow: pcwGetProductLineDetails.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
definition:
  operationId: pcwGetProductLineDetails
  tags:
    - pcw
  summary: Fetches product line details from Tia
  description: ""
  parameters:
    - in: query
      name: mainProductLineId
      required: false
      description: Main product line id to search for. Partial text search.
      schema:
        type: string
    - in: query
      name: mainProductLineVerNo
      required: false
      description: Main product line version number to search for. Partial text search.
      schema:
        type: string
    - in: query
      name: page
      required: false
      description: Page number
      schema:
        type: number
    - in: query
      name: prodLineIdAllVerList
      required: false
      description: Comma separated list of product line ids for which all versions
        must be shown.
      schema:
        type: string
    - in: query
      name: productLineId
      required: false
      description: Product line id to search for. Partial text search.
      schema:
        type: string
    - in: query
      name: productLineName
      required: false
      description: Product line name. Partial text search.
      schema:
        type: string
    - in: query
      name: productLineVerNo
      required: false
      description: Product line version number to search for. Partial text search.
      schema:
        type: string
    - in: query
      name: size
      required: false
      description: Page size
      schema:
        type: number
    - in: query
      name: sort
      required: false
      description: "Format: property(:asc|desc). Default order is ascending. If no
        sort parameter is provided, data is sorted by productLineId in
        descending order and productLineVerId in ascending order. Usage example:
        ...?sort=status:desc"
      schema:
        type: string
    - in: query
      name: status
      required: false
      description: "Status to search for. Possible search values: P, D"
      schema:
        type: string
    - in: query
      name: versionNotes
      required: false
      description: Version notes to search for. Partial text search.
      schema:
        type: string
    - in: query
      name: partialVersionSearch
      required: false
      description: Partial product line version number search. When true, search is
        done as text on formatted and internal version numbers matching from
        left to right.
      schema:
        type: boolean
    - in: query
      name: mainProductLineStatus
      required: false
      description: "Main product line status to search for. Possible search values: P, D"
      schema:
        type: string
  responses: {}
