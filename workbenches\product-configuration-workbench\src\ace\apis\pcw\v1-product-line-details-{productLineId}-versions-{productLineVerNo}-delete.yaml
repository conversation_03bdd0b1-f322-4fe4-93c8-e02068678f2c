path: /v1/product-line-details/{productLineId}/versions/{productLineVerNo}
method: delete
flow: pcwDeleteProductLineDetails.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
definition:
  operationId: pcwDeleteProductLineDetails
  tags:
    - pcw
  summary: Deletes product line details by given product line ID and product line
    version number.
  description: ""
  parameters:
    - in: path
      name: productLineId
      required: true
      description: ""
      schema:
        type: string
    - in: path
      name: productLineVerNo
      required: true
      description: ""
      schema:
        type: string
    - in: query
      name: cascade
      required: false
      description: ""
      schema:
        type: boolean
  responses: {}
