import '../support/commands.ts';

describe('PCW E2E test: field_type_PLACEHOLDER_validations', () => {
  beforeEach(() => {
    // Login to Keycloak
    cy.loginWithKeycloak();
    // Cypress starts out with a blank slate for each test
    // so we must tell it to visit our website with the `cy.visit()` command.
    cy.visit(Cypress.env('base_url') + '/pcw/landing');
  });

  afterEach(() => {
    //cy.logoutWithKeycloak();
  });

  const config = {
    connectionString: Cypress.env('connectionString'),
    user: Cypress.env('user'),
    password: Cypress.env('password')
  };

  const inputProductLineID = 'G2';
  const inputProductLineIdVersion = '1';

  it('PLACEHOLDER field without any data', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();
    cy.filterUIConfig('ADF_G2');
    cy.get('[data-testid="block-name-cell-1"] a').first().click();

    //create new AUTO field without any data
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);
    // select Field type
    cy.get('div[data-testid="field-type"]').click();
    cy.get('div[data-testid="field-type"]').find('div[role="button"]').should('have.attr', 'aria-expanded', 'true');
    cy.get('ul[role="listbox"] li').eq(5).click();
    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fieds
    cy.verifyFieldType('PLCHOLDER');
    // check Order
    cy.get('[data-testid="sort-no"]').parent().parent().siblings('span').should('be.visible').and('contain', 'Required field');
    //Check ErrorIcon is shown
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');
  });

  it('Sort no is already used for configuration', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();
    cy.filterUIConfig('ADF_G2');
    cy.get('[data-testid="block-name-cell-1"] a').first().click();
    //cy.clickButtonWithText('Add field');
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);
    // select Field type
    cy.get('div[data-testid="field-type"]').click();
    cy.get('div[data-testid="field-type"]').find('div[role="button"]').should('have.attr', 'aria-expanded', 'true');
    cy.get('ul[role="listbox"] li').eq(5).click();
    // enter value Order field
    cy.enterOrderValue('10');
    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fieds
    cy.verifyFieldType('PLCHOLDER');
    // check Order
    cy.get('[data-testid="sort-no"]')
      .parent()
      .parent()
      .siblings('span')
      .should('be.visible')
      .and('contain', 'Sort no is already used for configuration');
    cy.contains('span', 'Sort no is already used for configuration').should('be.visible');
    //Check ErrorIcon is shown
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');
  });

  it('Default value required', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();
    cy.filterUIConfig('ADF_G2');
    cy.get('[data-testid="block-name-cell-1"] a').first().click();
    // cy.clickButtonWithText('Add field');
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);
    // select Field type
    cy.get('div[data-testid="field-type"]').click();
    cy.get('div[data-testid="field-type"]').find('div[role="button"]').should('have.attr', 'aria-expanded', 'true');
    cy.get('ul[role="listbox"] li').eq(5).click();
    // enter value Order field
    cy.enterOrderValue('88');
    //expand Default value type dropdown and choose value
    cy.chooseDefaultValueType('Customer detail');
    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fieds
    cy.verifyFieldType('PLCHOLDER');
    /*
    cy.get('#default-value-dropdown-searchable')
      .parents('div[class*="MuiAutocomplete-root"]')
      .siblings('span')
      .contains('Required field')
      .should('be.visible');
      */
    //Check ErrorIcon is shown
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');
  });

  it('Default LOV required', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();
    cy.filterUIConfig('ADF_G2');
    cy.get('[data-testid="block-name-cell-1"] a').first().click();
    // cy.clickButtonWithText('Add field');
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);
    // select Field type
    cy.get('div[data-testid="field-type"]').click();
    cy.get('div[data-testid="field-type"]').find('div[role="button"]').should('have.attr', 'aria-expanded', 'true');
    cy.get('ul[role="listbox"] li').eq(5).click();
    // enter value Order field
    cy.enterOrderValue('88');
    //expand Default value type dropdown and choose value
    cy.chooseDefaultValueType('Fixed value');
    //enter Default value
    cy.enterDefaultValue('111');
    //choose LOV type
    cy.chooseLOVType('TARIFF_CODE');
    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fieds
    cy.verifyFieldType('PLCHOLDER');
    // Assert that the "Required field" text is visible
    /*
    cy.get('.MuiTypography-root.MuiTypography-H200').should('be.visible').and('contain', 'Required field');
    */
    //Check ErrorIcon is shown
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');
    cy.chooseLOVType('TASK_FLOW');
    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fieds
    cy.verifyFieldType('PLCHOLDER');
    // Assert that the "Required field" text is visible
    /*
    cy.get('.MuiTypography-root.MuiTypography-H200').should('be.visible').and('contain', 'Required field');
    */
    //Check ErrorIcon is shown
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');
    cy.chooseLOVType('XLA_REFERENCE');
    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fieds
    cy.verifyFieldType('PLCHOLDER');
    // Assert that the "Required field" text is visible
    /*
    cy.get('.MuiTypography-root.MuiTypography-H200').should('be.visible').and('contain', 'Required field');
    */
    //Check ErrorIcon is shown
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');
    cy.chooseLOVType('XLA_REFERENCE_BIG');
    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fieds
    cy.verifyFieldType('PLCHOLDER');
    // Assert that the "Required field" text is visible
    /*
    cy.get('.MuiTypography-root.MuiTypography-H200').should('be.visible').and('contain', 'Required field');
    */
    //Check ErrorIcon is shown
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');
  });
});
