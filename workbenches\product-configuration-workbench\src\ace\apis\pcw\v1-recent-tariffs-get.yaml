path: /v1/recent-tariffs
method: get
flow: pcwGetRecentTariffs.yaml
secured: false
errorHandlers:
  - PCW_UNAUTHORIZED.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_BAD_REQUEST.yaml
definition:
  operationId: pcwGetRecentTariffs
  tags:
    - pcw
  summary: Fetches list of recently changed tariffs.
  description: ""
  parameters:
    - in: query
      name: page
      required: false
      description: Page number
      schema:
        type: number
    - in: query
      name: size
      required: false
      description: Page size
      schema:
        type: number
    - in: query
      name: sort
      required: false
      description: "Sort parameter. Format: property(:asc|desc)."
      schema:
        type: string
    - in: query
      name: recordUserid
      required: false
      description: Filter by record user id
      schema:
        type: string
    - in: query
      name: language
      required: false
      description: In which language data should be fetched
      schema:
        type: string
  responses: {}
