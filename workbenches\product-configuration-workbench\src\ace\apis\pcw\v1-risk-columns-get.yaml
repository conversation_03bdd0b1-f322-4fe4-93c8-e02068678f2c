path: /v1/risk-columns
method: get
flow: pcwGetRiskColumns.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
definition:
  operationId: pcwGetRiskColumns
  tags:
    - pcw
  summary: Get list of product line UI risk columns.
  description: ""
  parameters:
    - in: query
      name: page
      required: false
      description: Page number
      schema:
        type: number
    - in: query
      name: size
      required: false
      description: Page size
      schema:
        type: number
    - in: query
      name: columnName
      required: false
      description: Column name
      schema:
        type: string
  responses: {}
