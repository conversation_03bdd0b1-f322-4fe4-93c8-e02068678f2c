path: /v1/ui-configuration-fields/{uiConfigurationFieldId}
method: put
flow: pcwUpdateUIConfigurationFields.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
definition:
  operationId: pcwUpdateUIConfigurationFields
  tags:
    - pcw
  summary: ""
  description: ""
  parameters:
    - in: path
      name: uiConfigurationFieldId
      required: true
      description: ""
      schema:
        type: number
  requestBody:
    content:
      application/json: {}
  responses: {}
