path: /v1/ui-configurations
method: get
flow: pcwGetUIconfigurations.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
definition:
  operationId: pcwGetUIconfigurations
  tags:
    - pcw
  summary: ""
  description: ""
  parameters:
    - in: query
      name: productLineId
      required: true
      description: Product Line Id to search for
      schema:
        type: string
    - in: query
      name: objectType
      required: false
      description: Object Type to search for.
      schema:
        type: string
    - in: query
      name: productLineVersion
      required: false
      description: Product Line Version to search for.
      schema:
        type: string
    - in: query
      name: language
      required: false
      description: Language
      schema:
        type: string
    - in: query
      name: uiConfigName
      required: false
      description: Name of Product Line UI Configuration to search for. Partial search
        supported.
      schema:
        type: string
    - in: query
      name: uiConfigDescription
      required: false
      description: Description of Product Line UI Configuration to search for. Partial
        search supported.
      schema:
        type: string
    - in: query
      name: uiConfigVersion
      required: false
      description: Version of Product Line UI Configuration to search for.
      schema:
        type: string
    - in: query
      name: uiConfigStatus
      required: false
      description: Status of Product Line UI Configuration to search for.
      schema:
        type: string
    - in: query
      name: page
      required: false
      description: Result page you want to retrieve (1..N)
      schema:
        type: number
    - in: query
      name: size
      required: false
      description: Number of records per page
      schema:
        type: number
    - in: query
      name: sort
      required: false
      description: "Format: property(:asc|desc). Default order is ascending. If no
        sort parameter is provided, data is sorted by uiConfigName in ascending
        order. Usage example: ...?sort=status:desc"
      schema:
        type: string
  responses: {}
