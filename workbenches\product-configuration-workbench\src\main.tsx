import { config, registerConfig } from '@sapiens/workbench-feature-library';
import React from 'react';
import ReactDOM from 'react-dom/client';

import InitApp from './InitApp';

registerConfig(import.meta.env.PROD, import.meta.env.BASE_URL).then(async () => {
  if (import.meta.env.DEV && config.mswOn) {
    const { worker } = await import('@mocks/browser');
    worker.start();
  }

  ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(<InitApp />);
});
