{"name": "product-configuration-workbench", "private": true, "version": "1.1.0-rc.522", "type": "module", "scripts": {"start": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "testOnly": "vitest run", "vitest-preview": "vitest-preview", "coverage": "vitest run --coverage", "cypress:open": "cypress open  --config-file ../cypress-utilities/cypress.config.ts", "cypress:run": "cypress run  --reporter mochawesome --reporter-options reportDir=\"./results\",overwrite=false,html=false,json=true --config-file ../cypress-utilities/cypress.config.ts", "cypress:run:headless": "cypress run  --browser electron --headless --reporter mochawesome --reporter-options reportDir=\"./results\",overwrite=false,html=false,json=true --config-file ../cypress-utilities/cypress.config.ts", "mochawesome:merge": "mochawesome-merge ./results/*.json > ./merged.json", "mochawesome:report": "marge --inline ./merged.json --reportFilename report --timestamp false", "clean:reports": "del mochawesome-report/* results/* merged.json", "lint": "npx eslint . --ext .ts,.tsx --fix"}, "dependencies": {"@hookform/resolvers": "^3.1.1", "@mui/icons-material": "^5.14.1", "@mui/material": "^5.13.3", "@mui/styled-engine": "npm:@mui/styled-engine-sc@latest", "@mui/x-date-pickers": "^6.19.8", "@reduxjs/toolkit": "^1.9.5", "@sapiens/insuredpro-ui-component-library": "*", "@sapiens/workbench-feature-library": "*", "@shared-config/eslint": "*", "@shared-config/prettier": "*", "@tanstack/react-query": "^4.29.13", "@tanstack/react-query-devtools": "^4.32.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "axios": "^1.8.3", "i18next": "^23.5.1", "lint-staged": "^13.2.1", "lodash": "^4.17.21", "oidc-client-ts": "^2.2.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.0", "react-i18next": "^13.0.1", "react-intersection-observer": "^9.5.2", "react-oidc-context": "^2.2.2", "react-redux": "^8.0.5", "react-router-dom": "^6.10.0", "react-toastify": "9.1.2", "styled-components": "^5.3.10", "xvfb": "^0.4.0", "zod": "^3.22.4", "dayjs": "1.11.10"}, "devDependencies": {"@hookform/devtools": "^4.3.1", "@tanstack/eslint-plugin-query": "^4.29.9", "@types/lodash": "^4.14.194", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "@vitejs/plugin-react": "^4.0.4", "@vitest/coverage-v8": "^0.34.4", "cypress": "^13.6.1", "del-cli": "^5.1.0", "eslint": "^8.2.0", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0", "jsdom": "^22.0.0", "mocha-junit-reporter": "^2.2.1", "mochawesome": "^7.1.3", "mochawesome-merge": "^4.3.0", "mochawesome-report-generator": "^6.2.0", "msw": "^1.2.2", "prettier": "^2.8.7", "typescript": "^4.9.3", "vite": "^4.4.9", "vite-plugin-svgr": "^3.2.0", "vitest": "^0.34.4", "vitest-preview": "^0.0.1"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "resolutions": {"@mui/styled-engine": "npm:@mui/styled-engine-sc@latest"}, "msw": {"workerDirectory": "public"}}