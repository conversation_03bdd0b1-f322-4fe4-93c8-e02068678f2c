path: /v1/subobject-columns
method: get
flow: pcwGetSubObjectColumns.yaml
secured: false
errorHandlers:
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
  - PCW_BAD_REQUEST.yaml
definition:
  operationId: pcwGetSubObjectColumns
  tags:
    - pcw
  summary: ""
  description: ""
  parameters:
    - in: query
      name: subobjectTypeId
      required: true
      description: subobjectTypeId to search for
      schema:
        type: string
    - in: query
      name: subobjectTypeVersion
      required: true
      description: subobjectTypeVersion to search for
      schema:
        type: number
    - in: query
      name: columnName
      required: false
      description: columnName to search for
      schema:
        type: string
    - in: query
      name: itemName
      required: false
      description: itemName to search for
      schema:
        type: string
    - in: query
      name: page
      required: false
      description: ""
      schema:
        type: number
    - in: query
      name: size
      required: false
      description: ""
      schema:
        type: number
  responses: {}
