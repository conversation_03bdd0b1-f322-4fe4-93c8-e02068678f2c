import { tracesMockFunction } from '@mocks/handlers';
import { axios, config } from '@sapiens/workbench-feature-library';
import { render, waitFor } from '@testing-library/react';
import React, { useEffect } from 'react';

import InitApp from './InitApp';

let receivedTraceparent: string | undefined = undefined;

describe('InitApp', () => {
  test('Should be mounted', async () => {
    const { container } = setup();

    expect(container).toBeTruthy();

    // Wait for Axios call to complete
    await waitFor(() => {
      expect(receivedTraceparent).toBeDefined();
      expect(receivedTraceparent).toMatch(/^00-/); // traceparent format
    });

    await waitFor(
      () => {
        expect(tracesMockFunction).toHaveBeenCalled(); // ✅ spy verification
      },
      { timeout: 60000 }
    );
  });
});

const setup = () => {
  config.otelTracesServiceName = 'test-service';
  config.otelTracesCollectorEndpoint = 'http://localhost:4318';
  config.otelTracesHeaderCorsUrls = '/api/test';
  config.otelEnableinstrumentation = 'all';

  const TestComponent = () => {
    useEffect(() => {
      axios.get('/api/test').then((resp) => {
        receivedTraceparent = resp.headers['traceparent'];
      });
    }, []);
    return <InitApp />;
  };

  const { container } = render(<TestComponent />);

  return {
    container
  };
};
