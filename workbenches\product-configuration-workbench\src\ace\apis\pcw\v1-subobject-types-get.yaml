path: /v1/subobject-types
method: get
flow: pcwGetSubobjectTypes.yaml
secured: false
errorHandlers:
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
  - PCW_BAD_REQUEST.yaml
definition:
  operationId: pcwGetSubobjectTypes
  tags:
    - pcw
  summary: ""
  description: ""
  parameters:
    - in: query
      name: prodLineId
      required: false
      description: Product Line identification to search for. Partial text search supported.
      schema:
        type: string
    - in: query
      name: prodLineVerNo
      required: false
      description: Product Line list version to search for.
      schema:
        type: string
    - in: query
      name: objectTypeId
      required: false
      description: Object type id to search for. Partial text search supported
      schema:
        type: string
    - in: query
      name: objectTypeVer
      required: false
      description: Object type version to search for.
      schema:
        type: string
    - in: query
      name: subobjectTypeId
      required: false
      description: Subobject type id to search for. Partial text search supported.
      schema:
        type: string
    - in: query
      name: subobjectTypeVer
      required: false
      description: Subobject type version to search for.
      schema:
        type: string
    - in: query
      name: subobjectTypeDesc
      required: false
      description: Subobject Type Description to search for. Partial text search supported.
      schema:
        type: string
    - in: query
      name: subobjectTypeListVer
      required: false
      description: Subobject Type List Version to search for.
      schema:
        type: string
    - in: query
      name: page
      required: false
      description: Result page you want to retrieve (1..N)
      schema:
        type: string
    - in: query
      name: size
      required: false
      description: Number of records per page
      schema:
        type: string
    - in: query
      name: sort
      required: false
      description: "Format: property(:asc|desc). Default order is ascending. If no
        sort parameter is provided, data is sorted by subobjectTypeId in
        ascending order. Usage example: ...?sort=id:desc"
      schema:
        type: string
  responses: {}
