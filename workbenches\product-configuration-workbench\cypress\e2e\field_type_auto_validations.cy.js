import '../support/commands.ts';

describe('PCW E2E test: field_type_AUTO_validations', () => {
  beforeEach(() => {
    // Login to Keycloak
    cy.loginWithKeycloak();
    // Cypress starts out with a blank slate for each test
    // so we must tell it to visit our website with the `cy.visit()` command.
    cy.visit(Cypress.env('base_url') + '/pcw/landing');
  });

  afterEach(() => {
    //cy.logoutWithKeycloak();
  });

  const config = {
    connectionString: Cypress.env('connectionString'),
    user: Cypress.env('user'),
    password: Cypress.env('password')
  };

  const inputProductLineID = 'G2';
  const inputProductLineIdVersion = '1';

  it('AUTO read-only fields', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();
    cy.filterUIConfig('ADF_G2');
    cy.get('[data-testid="block-name-cell-1"] a').first().click();
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);

    cy.get('#function-dropdown-searchable').should('be.disabled');
    cy.get('#default-value-dropdown-searchable').should('be.disabled');
    cy.get('input#lov-dropdown-searchable').should('be.disabled');
    cy.get('input#filtering-function-dropdown-searchable').should('be.disabled');
    cy.get('div[data-testid="dependent-columns"] input').should('be.disabled');
  });

  it('AUTO field without any data', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();
    cy.filterUIConfig('ADF_G2');
    cy.get('[data-testid="block-name-cell-1"] a').first().click();
    //create new AUTO field without any data
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);
    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fieds
    cy.verifyFieldType('AUTO');

    //Check Column Name
    cy.get('#column-name-dropdown-searchable')
      .parentsUntil('div[role="combobox"]') // Navigate up the DOM until the desired parent element
      .parent() // Select the parent of the final element in the chain
      .find('span') // Find the span element within the parent
      .contains('Required field') // Check if it contains the "Required field" text
      .should('be.visible'); // Assert that it is visible
    // check Order
    cy.get('[data-testid="sort-no"]').parent().parent().siblings('span').should('be.visible').and('contain', 'Required field');
    //Check Length
    cy.get('[data-testid="length"]').parent().parent().siblings('span').should('be.visible').and('contain', 'Required field');
    //Check ErrorIcon is shown
    cy.contains('span', 'Required field').should('be.visible');
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');
  });

  it('Column name is already used for configuration', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();
    cy.filterUIConfig('ADF_G2');
    cy.get('[data-testid="block-name-cell-1"] a').first().click();
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);
    //select Column name
    cy.selectColumnName('AGE_GROUP N02');
    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fieds
    cy.verifyFieldType('AUTO');
    //Check Column Name
    cy.get('#column-name-dropdown-searchable')
      .parentsUntil('div[role="combobox"]')
      .parent()
      .find('span')
      .contains('Column name is already used for configuration')
      .should('be.visible');

    // check Order
    cy.get('[data-testid="sort-no"]').parent().parent().siblings('span').should('be.visible').and('contain', 'Required field');
    //Check Length
    cy.get('[data-testid="length"]').parent().parent().siblings('span').should('be.visible').and('contain', 'Required field');

    cy.contains('span', 'Required field').should('be.visible');
    //Check ErrorIcon is shown
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');
  });

  it('Sort no is already used for configuration', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();
    cy.filterUIConfig('ADF_G2');
    cy.get('[data-testid="block-name-cell-1"] a').first().click();
    //cy.clickButtonWithText('Add field');
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);
    //select Column name
    cy.selectColumnName('USERID');
    // enter value Order field
    cy.enterOrderValue('10');
    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fieds
    cy.verifyFieldType('AUTO');
    // check Order
    cy.get('[data-testid="sort-no"]')
      .parent()
      .parent()
      .siblings('span')
      .should('be.visible')
      .and('contain', 'Sort no is already used for configuration');
    //Check Length
    cy.get('[data-testid="length"]').parent().parent().siblings('span').should('be.visible').and('contain', 'Required field');

    cy.contains('span', 'Required field').should('be.visible');
    //Check ErrorIcon is shown
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');
  });
  /*
  it('Default value required', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();
    cy.filterUIConfig('ADF_G2');
    cy.get('[data-testid="block-name-cell-1"] a').first().click();
    // cy.clickButtonWithText('Add field');
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);
    //select Column name
    cy.selectColumnName('USERID');
    // enter value Order field
    cy.enterOrderValue('88');
    // Choose Length
    cy.selectOptionFromDropdown('Large');
    //expand Default value type dropdown and choose value
    cy.chooseDefaultValueType('Customer detail');
    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fieds
    cy.verifyFieldType('AUTO');
    cy.get('[data-testid="default-value"]')
    .parent()
    .parent() 
    .siblings('span') 
    .should('be.visible')
    .and('contain', 'Required field');
      
    //Check ErrorIcon is shown
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');
    cy.chooseDefaultValueType('User function');
    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fieds
    cy.verifyFieldType('AUTO');
    
    cy.get('[data-testid="default-value"]')
    .parent()
    .parent() 
    .siblings('span') 
    .should('be.visible')
    .and('contain', 'Required field');
      
      
    //Check ErrorIcon is shown
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');
    cy.chooseDefaultValueType('Name detail');
    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fieds
    cy.verifyFieldType('AUTO');
    
    cy.get('[data-testid="default-value"]')
    .parent()
    .parent() 
    .siblings('span') 
    .should('be.visible')
    .and('contain', 'Required field');
      
      
    //Check ErrorIcon is shown
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');
  });

  it('Default LOV required', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();
    cy.filterUIConfig('ADF_G2');
    cy.get('[data-testid="block-name-cell-1"] a').first().click();
    // cy.clickButtonWithText('Add field');
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);
    //select Column name
    cy.selectColumnName('USERID');
    // enter value Order field
    cy.enterOrderValue('88');
    // Choose Length
    cy.selectOptionFromDropdown('Large');
    //expand Default value type dropdown and choose value
    cy.chooseDefaultValueType('Fixed value');
    //enter Default value
    cy.enterDefaultValue('111');
    //choose LOV type
    cy.chooseLOVType('TARIFF_CODE');

    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fieds
    cy.verifyFieldType('AUTO');
    // Assert that the "Required field" text is visible
    cy.get('[data-testid="lov-dropdown-searchable"]')
    .parent()
    .parent() 
    .siblings('span') 
    .should('be.visible')
    .and('contain', 'Required field');
    //Check ErrorIcon is shown
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');
    cy.chooseLOVType('TASK_FLOW');
    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fieds
    cy.verifyFieldType('AUTO');
    // Assert that the "Required field" text is visible
    cy.get('[data-testid="lov-dropdown-searchable"]')
    .parent()
    .parent() 
    .siblings('span') 
    .should('be.visible')
    .and('contain', 'Required field');
    //Check ErrorIcon is shown
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');
    cy.chooseLOVType('XLA_REFERENCE');
    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fieds
    cy.verifyFieldType('AUTO');
    // Assert that the "Required field" text is visible
    cy.get('[data-testid="lov-dropdown-searchable"]')
    .parent()
    .parent() 
    .siblings('span') 
    .should('be.visible')
    .and('contain', 'Required field');
    //Check ErrorIcon is shown
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');
    cy.chooseLOVType('XLA_REFERENCE_BIG');
    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fieds
    cy.verifyFieldType('AUTO');
    // Assert that the "Required field" text is visible
    cy.get('[data-testid="lov-dropdown-searchable"]')
    .parent()
    .parent() 
    .siblings('span') 
    .should('be.visible')
    .and('contain', 'Required field');
    //Check ErrorIcon is shown
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');

  });
  */
});
