pcwInsertProductLineDetailRequest:
  required:
    - productLineId
    - productLineVerNo
  type: object
  properties:
    productLineId:
      maxLength: 5
      minLength: 1
      type: string
      description: Unique identification code for a product line entity
      example: "44"
    productLineVerNo:
      type: string
      description: Uniquely identifies this version of product line.
      example: "1"
    discountLevel:
      maximum: 9
      type: integer
      description: not current use
      format: int64
      example: 7
    flatPremium:
      maxLength: 1
      minLength: 0
      type: string
      description: |
        Does flat_premium apply ? Y/N
        (As opposite to pro-rata premium.)
        If yes then system will debit returned premium
        from tariff calculation without making any
        pro-rata calculation on cover period.
        One cannot make Midterms adjustments to a
        flat premium product line. If done then the
        flat_premium will be debited once again and
        there will be no return premium.
        If the MTA cover start date equals the original
        cover start date then premium will be refunded
        (nullified) in order to give possibilites for
        corrections
      example: N
    renew:
      type: string
      description: |
        Shall this product_line be renewed ? Y/N
        (flat premium product lines will not be
        renewed). Default is N.
      example: N
    temporaryInsurance:
      maxLength: 1
      minLength: 0
      type: string
      description: |
        A temporary insurance is created for a period where start and
        end date is specified.
        The insurance is not renewed.
        If the insurance is altered then the premium for the previous version
        is refunded and a new premium is collected for the new period specified
        (the previous version is made obsolete).
        The premium is collected as a whole (not in installments).
        This code controls the temporary insurance flag on the policy line,
        which can be set to YES or NO (null).
        Values:
        null    Temporary insurance NOT ALLOWED (default).
        1       NO is default on creation but can be changed to YES.
        2       YES is default on creation but can be changed to NO.
        3       YES is default on creation and can not be changed.
      example: "1"
    comProductGroup:
      maxLength: 3
      minLength: 0
      type: string
      description: |
        If commission to agents etc is
        relevant for this product_line, then
        a commission product group has to
        be filled in.
        If not filled in there will not be
        calculated any commisions.
      example: "*"
    conditionSet1:
      maxLength: 15
      minLength: 0
      type: string
      description: The identification of the first set of conditions for this product
        version
      example: Example String Value
    conditionSet2:
      maxLength: 15
      minLength: 0
      type: string
      description: The identification of the second set of conditions for this product
        version
      example: Example String Value
    conditionSet3:
      maxLength: 15
      minLength: 0
      type: string
      description: The identification of the third set of conditions for this product
        version
      example: Example String Value
    objTypeListVer:
      maximum: 999999
      type: integer
      description: Version of list of object window types linked to this version of
        product line
      format: int64
      example: 1
    riskSplitFixed:
      maxLength: 1
      minLength: 0
      type: string
      description: |
        F = fixed split
        V = Variable split
        N = no risk split
        With premium split it is possible to subdivide
        the total premium in its components like fire,
        theft, taxes etc.
        Fixed split represents a static split with fixed
        factors while variable split is used when the
        split is different from policy to policy and is
        dependant on the individual data, eg. sum
        insured for fire and sum insured for theft.
        In this case no splitfactors are defined, since
        the tariff_algorithm will calculate the
        individual components.
      example: V
    riskSplitVer:
      maximum: 999999
      type: integer
      description: Version of list of risk_split, which links to this version of
        product line
      format: int64
      example: 1
    tariffTypeListVer:
      maximum: 999999
      type: integer
      description: |+
        
        Version of list tariff table components
        (tariff_structure), which links to this
        version of product line

      format: int64
      example: 1
    tariffCalcVer:
      maximum: 999999
      type: integer
      description: |+
        
        Version of tariff algorithm (user_function
        type TRF), which links to this version of
        product line.

      format: int64
      example: 1
    renRuleVer:
      maximum: 999999
      type: integer
      description: Version of renewal rule (user_function type REN), which links to
        this version of product line
      format: int64
      example: 1
    coverCheckVer:
      maximum: 999999
      type: integer
      description: Version of cover check (user_function type COV), which links to
        this version of product line
      format: int64
    versionNotes:
      maxLength: 2000
      minLength: 1
      type: string
      description: Notes on why current version is created
      example: TIA Test Building
    status:
      maxLength: 1
      minLength: 0
      type: string
      description: |+
        
        D = Development
        P = Production.
        The row can not change if status = P.

      example: D
    structureKey:
      maxLength: 12
      minLength: 0
      type: string
      description: this is the key to a global structure to be used across several
        product lines/products
      example: REG_NO_GTS
    structureListVer:
      maximum: 999999
      type: integer
      description: this is the version of the global structure
      format: int64
      example: 1
    clauseListVer:
      maximum: 999999
      type: integer
      description: this is the version of the global clause
      format: int64
      example: 1
    informationOnly:
      maxLength: 1
      minLength: 0
      type: string
      description: Information only.
      example: N
    defaultBackdatingType:
      type: string
      description: Default backdating type. Default is 3
      example: "3"
    mainProductLineId:
      maxLength: 5
      minLength: 0
      type: string
      description: References main product line id
      example: HHOLD
    mainProductLineVerNo:
      type: string
      description: References main product line version
      example: "1"
    uiConfigListVer:
      maximum: 999999
      type: integer
      description: this is the version of the ui configuration list.
      format: int64
      example: 1
    validationListVer:
      maximum: 999999
      type: integer
      description: Validation list version
      format: int64
      example: 1
    shortDescTable1:
      maxLength: 30
      minLength: 0
      type: string
      description: "Name of a table to be used for retrieving data, that is a part of a short description for policy line record. Possible values: AGREEMENT_LINE/OBJECT.objectTypeId"
      example: OBJECT.ZC
    shortDescColumn1:
      maxLength: 30
      minLength: 0
      type: string
      description: Name of the actual column from table, defined in SHORT_DESC_TABLE_X
        field.
      example: AGR_LINE_NO
    shortDescText1:
      maxLength: 30
      minLength: 0
      type: string
      description: Fixed text value to be used for combining a short description for a
        policy line record.
      example: BW
    shortDescTable2:
      maxLength: 30
      minLength: 0
      type: string
      description: "Name of a table to be used for retrieving data, that is a part of a short description for policy line record. Possible values: AGREEMENT_LINE/OBJECT.objectTypeId"
      example: AGREEMENT_LINE
    shortDescColumn2:
      maxLength: 30
      minLength: 0
      type: string
      description: Name of the actual column from table, defined in SHORT_DESC_TABLE_X
        field.
      example: USERID
    shortDescText2:
      maxLength: 30
      minLength: 0
      type: string
      description: Fixed text value to be used for combining a short description for a
        policy line record.
      example: " / "
    shortDescTable3:
      maxLength: 30
      minLength: 0
      type: string
      description: "Name of a table to be used for retrieving data, that is a part of a short description for policy line record. Possible values: AGREEMENT_LINE/OBJECT.objectTypeId"
      example: OBJECT.ZC
    shortDescColumn3:
      maxLength: 30
      minLength: 0
      type: string
      description: Name of the actual column from table, defined in SHORT_DESC_TABLE_X
        field.
      example: TIMESTAMP
    shortDescText3:
      maxLength: 30
      minLength: 0
      type: string
      description: Fixed text value to be used for combining a short description for a
        policy line record.
      example: Example String Value
    shortDescTable4:
      maxLength: 30
      minLength: 0
      type: string
      description: "Name of a table to be used for retrieving data, that is a part of a short description for policy line record. Possible values: AGREEMENT_LINE/OBJECT.objectTypeId"
      example: Example String Value
    shortDescColumn4:
      maxLength: 30
      minLength: 0
      type: string
      description: Name of the actual column from table, defined in SHORT_DESC_TABLE_X
        field.
      example: Example String Value
    shortDescText4:
      maxLength: 30
      minLength: 0
      type: string
      description: Fixed text value to be used for combining a short description for a
        policy line record.
      example: Example String Value
    yearsToKeepQ:
      type: integer
      description: Years to keep quote before it may be deleted
      format: int64
      example: 5
    yearsToKeepP:
      type: integer
      description: Years to keep policy before it may be deleted
      format: int64
      example: 15
    indexationListVer:
      maximum: 999999
      type: integer
      description: this is the version of the indexation rule list.
      format: int64
      example: 1
    productLineName:
      type: string
      description: Product Line description.
      example: Example String Value
  description: Model documentation for product line details.
