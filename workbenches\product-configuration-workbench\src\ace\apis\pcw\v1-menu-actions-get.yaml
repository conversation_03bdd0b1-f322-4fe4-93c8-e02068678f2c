path: /v1/menu-actions
method: get
flow: pcwGetMenuActions.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
definition:
  operationId: pcwGetMenuActions
  tags:
    - pcw
  summary: ""
  description: ""
  parameters:
    - in: query
      name: actionType
      required: false
      description: Action type to search for.
      schema:
        type: string
    - in: query
      name: command
      required: false
      description: Command to search for.
      schema:
        type: string
    - in: query
      name: additionalFilters
      required: false
      description: 'Format: (Property name) (Operator name) (Values) (AND) (Property
        name) (Operator name) (Values). The Property name field should be in
        response model class. Usage example: createDate GT
        "2024-01-23T07:35:31.000Z" AND amount EQ 2293.56 AND itemText LIKE "01
        PET Default" Based on the property name datatype, The suitable operator
        has to be given String:EQ,NEQ,LIKE,NLIKE,IN,NIN,IS,ISN
        Boolean:EQ,NEQ,IS,ISN Other types:EQ,NEQ,GT,LT,GTE,LTE,IN,NIN,IS,ISN'
      schema:
        type: string
    - in: query
      name: page
      required: false
      description: Result page you want to retrieve (1..N)
      schema:
        type: integer
    - in: query
      name: size
      required: false
      description: Number of records per page
      schema:
        type: integer
    - in: query
      name: sort
      required: false
      description: "Format: property(:asc|desc). Default order is ascending. If no
        sort parameter is provided, data is sorted by id in ascending order.
        Usage example: /v1?sort=id:desc"
      schema:
        type: string
  responses: {}
