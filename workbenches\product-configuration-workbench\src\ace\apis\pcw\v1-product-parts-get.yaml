path: /v1/product-parts
method: get
flow: pcwGetProductParts.yaml
secured: false
errorHandlers:
  - PCW_UNAUTHORIZED.yaml
  - PCW_BAD_REQUEST.yaml
  - PCW_NOT_FOUND.yaml
definition:
  operationId: pcwGetProductParts
  tags:
    - pcw
  summary: ""
  description: ""
  parameters:
    - in: query
      name: id
      required: false
      description: id
      schema:
        type: number
    - in: query
      name: productId
      required: false
      description: product Id
      schema:
        type: string
    - in: query
      name: productName
      required: false
      description: product Name
      schema:
        type: string
    - in: query
      name: productLineId
      required: false
      description: product Line Id
      schema:
        type: string
    - in: query
      name: productLineName
      required: false
      description: product Line Name
      schema:
        type: string
    - in: query
      name: sectionNo
      required: false
      description: section No
      schema:
        type: string
    - in: query
      name: optionalSection
      required: false
      description: optional Section
      schema:
        type: boolean
    - in: query
      name: sectionLevel
      required: false
      description: sectionLevel
      schema:
        type: number
    - in: query
      name: dependantOnProductLine
      required: false
      description: dependant on product line
      schema:
        type: string
    - in: query
      name: productLineVersionNo
      required: false
      description: product Line Version No
      schema:
        type: string
    - in: query
      name: productLineVersionStatus
      required: false
      description: productLineVersionStatus
      schema:
        type: string
    - in: query
      name: listVersion
      required: false
      description: listVersion
      schema:
        type: string
    - in: query
      name: listStatus
      required: false
      description: listStatus
      schema:
        type: string
    - in: query
      name: exclusiveSection
      required: false
      description: exclusiveSection
      schema:
        type: string
    - in: query
      name: additionalFilters
      required: false
      description: additionalFilters
      schema:
        type: string
    - in: query
      name: page
      required: false
      description: page
      schema:
        type: number
    - in: query
      name: size
      required: false
      description: size
      schema:
        type: number
    - in: query
      name: sort
      required: false
      description: sort
      schema:
        type: string
  responses: {}
