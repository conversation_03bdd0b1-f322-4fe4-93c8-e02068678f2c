import '../support/commands.ts';

describe('PCW E2E test: CRUD UI configuration', () => {
  beforeEach(() => {
    // Login to Keycloak
    cy.loginWithKeycloak();
    // Cypress starts out with a blank slate for each test
    // so we must tell it to visit our website with the `cy.visit()` command.
    cy.visit(Cypress.env('base_url') + '/pcw/landing');
  });

  afterEach(() => {
    //cy.logoutWithKeycloak();
  });

  const config = {
    connectionString: Cypress.env('connectionString'),
    user: Cypress.env('user'),
    password: Cypress.env('password')
  };

  const inputProductLineID = 'G2';
  const inputProductLineIdVersion = '1';

  it('CRUD UI configuration', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();

    //create New UI config
    cy.get('[data-testid="add-new-ui-config-button"]').click();
    cy.wait(1000);
    cy.get('[data-testid="ui-config-name-input"] input').type('aaa', { force: true });
    cy.get('[data-testid="ui-config-description-input"]').eq(0).type('test description');
    cy.get('[data-testid="ui-config-object-type-select"]').click();
    cy.get('[data-value="G2"]').click();
    cy.get('[data-testid="save-button"]').first().click();
    //cy.get('[data-testid="ui-config-success-alert"]').should('be.visible').contains('Changes were saved.');
    cy.contains('.MuiTypography-root.MuiTypography-medium', 'Success Changes were saved.').should('be.visible');
    cy.wait(1000);
    cy.get('[data-testid="risk-and-objects-tab"]').click();
    cy.get('[data-testid="ui-configuration-tab"]').click();

    //create new UI object block
    cy.get('[data-testid="new-block-button"]').eq(0).click();
    cy.get('[data-testid="block-name-input"]').type('test name1');
    cy.get('[data-testid="block-description-input"]').eq(0).type('test description1');
    cy.get('[data-testid="block-expanded-checkbox"] > :first-child').eq(0).check({ force: true });
    cy.get('[data-testid="block-read-only-checkbox"] > :first-child').eq(0).check({ force: true });
    cy.get('[data-testid="block-sort-no-input"]').eq(0).type('7');
    cy.get('[data-testid="save-button"]').first().click();
    //cy.get('[data-testid="ui-config-success-alert"]').should('be.visible').contains('Changes were saved.');
    cy.contains('.MuiTypography-root.MuiTypography-medium', 'Success Changes were saved.').should('be.visible');
    cy.wait(1000);

    //create new UI risk block
    cy.get('[data-testid="new-block-button"]').eq(0).click();
    cy.get('[data-testid="block-name-input"]').type('test name2');
    cy.get('[data-testid="block-description-input"]').eq(1).type('test description2');
    cy.get('[data-testid="block-type-select"]').eq(0).click();
    cy.get('[data-value="RISK"]').click();
    cy.get('[data-testid="save-button"]').first().click();
    //cy.get('[data-testid="ui-config-success-alert"]').should('be.visible').contains('Changes were saved.');
    cy.contains('.MuiTypography-root.MuiTypography-medium', 'Success Changes were saved.').should('be.visible');
    cy.wait(1000);

    //create new UI subobject block
    cy.get('[data-testid="new-block-button"]').eq(0).click();
    cy.get('[data-testid="block-name-input"]').type('test name3');
    cy.get('[data-testid="block-description-input"]').eq(2).type('test description3');
    cy.get('[data-testid="block-type-select"]').eq(0).click();
    cy.get('[data-value="OBJSLAVE"]').click();
    cy.get('[data-testid="block-obj-slave-type-id-select"]').eq(0).click();
    cy.get('[data-value="G2_DRIVER"]').click();
    cy.get('[data-testid="block-sort-no-input"]').eq(1).type('17');
    cy.get('[data-testid="save-button"]').first().click();
    //cy.get('[data-testid="ui-config-success-alert"]').should('be.visible').contains('Changes were saved.');
    cy.contains('.MuiTypography-root.MuiTypography-medium', 'Success Changes were saved.').should('be.visible');
    cy.wait(1000);

    //verify data
    cy.get('[data-testid="ui-config-name-filter-input"] > :first-child').type('aaa');

    //verify Ui config main data
    cy.get('[data-testid="ui-config-name-cell"]').contains('AAA');
    cy.get('[data-testid="ui-config-description-input"] input').eq(0).should('have.value', 'test description');
    cy.get('[data-testid="ui-config-version-select"]').contains('1');
    cy.get('[data-testid="ui-config-status-cell"]').contains('Dev');
    cy.get('[data-testid="ui-config-object-type-cell"]').contains('G2');

    //verify first row
    cy.get('[data-testid="block-name-cell-0"]').contains('test name2');
    cy.get('[data-testid="block-label-cell-0"]').contains('test description2');
    cy.get('[data-testid="block-description-input"] input').eq(0).should('have.value', 'test description2');
    cy.get('[data-testid="block-type-cell-0"]').contains('Risk');
    cy.get('[data-testid="block-expanded-checkbox"]').eq(0).find('svg#unchecked-icon').should('exist');
    cy.get('[data-testid="block-read-only-checkbox"]').eq(0).find('svg#unchecked-icon').should('exist');
    cy.get('[data-testid="block-order-cell-0"]').contains('6');

    //verify second row
    cy.get('[data-testid="block-name-cell-1"]').contains('test name1');
    cy.get('[data-testid="block-label-cell-1"]').contains('test description1');
    cy.get('[data-testid="block-description-input"] input').eq(1).should('have.value', 'test description1');
    cy.get('[data-testid="block-type-cell-1"]').contains('Object');
    cy.get('[data-testid="block-sort-no-input"] input').eq(0).should('have.value', '7');
    cy.get('[data-testid="block-expanded-checkbox"]').eq(1).find('svg#checked-icon').should('exist');
    cy.get('[data-testid="block-read-only-checkbox"]').eq(1).find('svg#checked-icon').should('exist');

    //verify third row
    cy.get('[data-testid="block-name-cell-2"]').contains('test name3');
    cy.get('[data-testid="block-label-cell-2"]').contains('test description3');
    cy.get('[data-testid="block-description-input"] input').eq(2).should('have.value', 'test description3');
    cy.get('[data-testid="block-type-cell-2"]').contains('Subobject');
    cy.get('[data-testid="block-object-slave-type-cell-2"]').contains('G2_Driver');
    cy.get('[data-testid="block-sort-no-input"] input').eq(1).should('have.value', '17');
    cy.get('[data-testid="block-expanded-checkbox"]').eq(2).find('svg#unchecked-icon').should('exist');
    cy.get('[data-testid="block-read-only-checkbox"]').eq(2).find('svg#unchecked-icon').should('exist');

    //delete first UI block
    cy.get('[data-testid="DeleteIcon"]').eq(2).click();
    cy.get('[data-testid="delete-modal-confirm-button"]').click();
    cy.wait(1000);

    //delete second UI block
    cy.get('[data-testid="DeleteIcon"]').eq(1).click();
    cy.get('[data-testid="delete-modal-confirm-button"]').click();
    cy.wait(1000);

    //delete UI config
    cy.get('[data-testid="DeleteIcon"]').eq(0).click();
    cy.get('[data-testid="delete-modal-confirm-button"]').click();

    //click on approve deletion
    //cy.get('[data-testid="delete-modal-confirm-button"]').click();
    //cy.get('[data-testid="ui-config-success-alert"]').should('be.visible').contains('success');
cy.contains('.MuiTypography-root.MuiTypography-medium', ' Data was deleted successfully.').should('be.visible');

    cy.wait(1000);
    cy.get('[data-testid="risk-and-objects-tab"]').click();
    cy.get('[data-testid="ui-configuration-tab"]').click();
    cy.get('[data-testid="risk-and-objects-tab"]').click();
    cy.get('[data-testid="ui-configuration-tab"]').click();


    //verify that needed records deleted
    cy.get('[data-testid="ui-config-name-filter-input"] > :first-child').clear().type('aaa');
    cy.get('.MuiTypography-root.MuiTypography-default').should('include.text', 'No UI configurations');
  });
});
