import '../support/commands.ts';

describe('PCW E2E test: field_type_BUTTON_validations', () => {
  beforeEach(() => {
    // Login to Keycloak
    cy.loginWithKeycloak();
    // Cypress starts out with a blank slate for each test
    // so we must tell it to visit our website with the `cy.visit()` command.
    cy.visit(Cypress.env('base_url') + '/pcw/landing');
  });

  afterEach(() => {
    //cy.logoutWithKeycloak();
  });

  const config = {
    connectionString: Cypress.env('connectionString'),
    user: Cypress.env('user'),
    password: Cypress.env('password')
  };

  const inputProductLineID = 'G2';
  const inputProductLineIdVersion = '1';

  it('BUTTON read-only fields', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();
    cy.filterUIConfig('ADF_G2');
    cy.get('[data-testid="block-name-cell-1"] a').first().click();
    //create new BUTTON field without any data
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);
    // select Field type
    cy.get('div[data-testid="field-type"]').click();
    cy.get('div[data-testid="field-type"]').find('div[role="button"]').should('have.attr', 'aria-expanded', 'true');
    cy.get('ul[role="listbox"] li').eq(1).click();
    //check read-only fields
    cy.get('#column-name-dropdown-searchable').should('be.disabled');
    cy.get('#default-value-dropdown-searchable').should('be.disabled');
    cy.get('div[role="button"][aria-expanded="false"][aria-haspopup="listbox"][aria-labelledby="inputLabel"][aria-disabled="true"]').should(
      'have.attr',
      'aria-disabled',
      'true'
    );
    cy.get('input#lov-dropdown-searchable').should('be.disabled');
    cy.get('input#filtering-function-dropdown-searchable').should('be.disabled');
    cy.get('div[data-testid="dependent-columns"] input').should('be.disabled');
    cy.get('#validation-rule-dropdown-searchable').should('be.disabled');
  });

  it('BUTTON field without any data', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();
    cy.filterUIConfig('ADF_G2');
    cy.get('[data-testid="block-name-cell-1"] a').first().click();
    //create new BUTTON field without any data
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);
    // select Field type
    cy.get('div[data-testid="field-type"]').click();
    cy.get('div[data-testid="field-type"]').find('div[role="button"]').should('have.attr', 'aria-expanded', 'true');
    cy.get('ul[role="listbox"] li').eq(1).click();
    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fieds
    cy.verifyFieldType('BUTTON');
    // check Order
    cy.get('[data-testid="sort-no"]').parent().parent().siblings('span').should('be.visible').and('contain', 'Required field');
    // check function required field
    cy.get('#function-dropdown-searchable')
      .parents('div[class*="MuiAutocomplete-root"]')
      .siblings('span')
      .contains('Required field')
      .should('be.visible');

    cy.contains('span', 'Required field').should('be.visible');
    //Check ErrorIcon is shown
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');
  });

  it('Default value required', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();
    cy.filterUIConfig('ADF_G2');
    cy.get('[data-testid="block-name-cell-1"] a').first().click();
    // cy.clickButtonWithText('Add field');
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);
    // select Field type
    cy.get('div[data-testid="field-type"]').click();
    cy.get('div[data-testid="field-type"]').find('div[role="button"]').should('have.attr', 'aria-expanded', 'true');
    cy.get('ul[role="listbox"] li').eq(1).click();
    // enter value Order field
    cy.enterOrderValue('88');
    // Choose Length
    cy.selectOptionFromDropdown('Large');
    cy.selectFunction('Button');
    //expand Default value type dropdown and choose value
    cy.chooseDefaultValueType('Customer detail');
    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fields
    cy.verifyFieldType('BUTTON');
    /*
    cy.get('[data-testid="default-value"]')
    .parent()
    .parent() 
    .siblings('span') 
    .should('be.visible')
    .and('contain', 'Required field');
    */
    //Check ErrorIcon is shown
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');
  });
});
