import '../support/commands.ts';
import { Database } from '../../../cypress-utilities/cypress/index';
import { productLineMainDataGv, productLineMainData44, productLineMainDataTbui } from '../fixtures/constants.cy.ts';
import { should } from 'chai';

describe('PCW E2E test: view mode in Risk window', () => {
  beforeEach(() => {
    // Login to Keycloak
    cy.loginWithKeycloak();
    // Cypress starts out with a blank slate for each test
    // so we must tell it to visit our website with the `cy.visit()` command.
    cy.visit(Cypress.env('base_url') + '/pcw/landing');
  });

  afterEach(() => {
    //cy.logoutWithKeycloak();
  });

  const config = {
    connectionString: Cypress.env('connectionString'),
    user: Cypress.env('user'),
    password: Cypress.env('password')
  };

  const inputProductLineID = 'G2';
  const inputProductLineIdVersion = '1';

  it('Testing Risk view mode window', () => {
    //assertion about UI configuration page
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    cy.verifyUiConfigurationPage();

    //find needed Subobject and enter the window
    cy.get('[data-testid="ui-config-name-filter-input"]').type('ADF').type('{enter}');
    cy.get('[data-testid="ui-config-name-cell"]').first().should('include.text', 'ADF');
    cy.get('[data-testid="block-name-cell-2"] a').should('be.visible').click();

    //assert all general Subobject window items
    cy.get('[data-testid="breadcrumbs"]').should('be.visible').should('include.text', 'Risk UI Configuration');
    cy.get('#UIConfigurationRiskTitle').should('include.text', 'Risk').and('include.text', 'G2').and('include.text', 'RISK');
    cy.get('[data-testid="AddCircleIcon"]').should('be.visible');

    //assert Risk filter area
    cy.get('input[name="riskNoOrDescription"]').should('be.visible');
    cy.get('[data-testid="UiConfigRisksDefaultSelectedDropdown"]').should('be.visible');
    cy.get('[data-testid="UiConfigRisksDefaultExpandedDropdown"]').should('be.visible');
    cy.get('[data-testid="UiConfigRisksExpandSelectedDropdown"]').should('be.visible');
    cy.get('[data-testid="UiConfigRisksMandatoryDropdown"]').should('be.visible');

    //assert Risk Configuration card
    cy.get('#UIConfigurationRiskTitle').should('be.visible');
    cy.get('[data-testid="UiConfigRiskRiskNo"]').first().should('be.visible').should('include.text', '1 Liability');
    cy.get('[data-testid="UiConfigRiskDefaultSelected"]').first().should('be.visible');
    cy.get('[data-testid="UiConfigRiskDefaultExpanded"]').first().should('be.visible');
    cy.get('[data-testid="UiConfigRiskExpandSelected"]').first().should('be.visible');
    cy.get('[data-testid="UiConfigRiskMandatory"]').first().should('be.visible');
    cy.get('[data-testid="DeleteIcon"]').first().should('be.visible');

    //assert Risk field configuration
    cy.get('#UIConfigurationRiskFieldTitle').should('include.text', 'Risk field configuration');

    //cy.get('.sc-dJiZtA.bfnavJ').eq(1).scrollIntoView()
    cy.get('#UIConfigurationRiskFieldTitle')
      .scrollIntoView()
      .should('be.visible')
      .within(() => {
        cy.contains('button', 'Add field').should('be.visible');
        cy.contains('button', 'Cancel').should('be.visible').click();
        cy.contains('button', 'Save').should('be.visible');
      });

    cy.get('input[name="columnName"]').should('be.visible');
    cy.get('input[name="columnLabel"]').should('be.visible');
    cy.get('input[name="sortNo"]').should('be.visible');
    cy.get('[data-testid="UiConfigRiskFieldLengthDropdown"]').should('be.visible');
    cy.get('[data-testid="UiConfigRiskFieldDataTypeDropdown"]').should('be.visible');

    cy.get('.sc-iapWAC > :nth-child(2)').should('be.visible');

    cy.get('#-dropdown-searchable').first().should('be.visible');
    cy.get('.sc-eDPEul.fAVFqz').first().should('be.visible');
    cy.get('[data-testid="field-sort-no-input"]').first().should('be.visible');
    cy.get('[data-testid="field-length-desc-select"]').first().should('be.visible');
    cy.get('[data-testid="field-number-format-select"]').first().should('be.visible');
  });
});
