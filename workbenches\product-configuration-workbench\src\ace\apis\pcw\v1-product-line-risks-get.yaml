path: /v1/product-line-risks
method: get
flow: pcwGetProductLineRisks.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
definition:
  operationId: pcwGetProductLineRisks
  tags:
    - pcw
  summary: Get list of product line risks by input parameters.
  description: ""
  parameters:
    - in: query
      name: page
      required: false
      description: Page number
      schema:
        type: number
    - in: query
      name: size
      required: false
      description: Page size
      schema:
        type: string
    - in: query
      name: productLineId
      required: true
      description: Product line ID
      schema:
        type: string
    - in: query
      name: productLineVerNo
      required: true
      description: Product line version
      schema:
        type: number
    - in: query
      name: riskNo
      required: false
      description: Risk number
      schema:
        type: number
    - in: query
      name: riskDescription
      required: false
      description: Risk description. Partial text search
      schema:
        type: string
  responses: {}
