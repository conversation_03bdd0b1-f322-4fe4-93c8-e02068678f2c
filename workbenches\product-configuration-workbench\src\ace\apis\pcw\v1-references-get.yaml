path: /v1/references
method: get
flow: pcwGetReferences.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
definition:
  operationId: pcwGetReferences
  tags:
    - pcw
  summary: Fetches reference definitions
  description: ""
  parameters:
    - in: query
      name: page
      required: false
      description: Page number
      schema:
        type: number
    - in: query
      name: size
      required: false
      description: Page size
      schema:
        type: number
    - in: query
      name: sort
      required: false
      description: "Sort parameter. Format: property(:asc|desc)."
      schema:
        type: string
    - in: query
      name: id
      required: false
      description: Unique id
      schema:
        type: number
    - in: query
      name: referenceId
      required: false
      description: Reference Id
      schema:
        type: string
    - in: query
      name: tableName
      required: false
      description: Reference table name
      schema:
        type: string
    - in: query
      name: version
      required: false
      description: Reference version
      schema:
        type: number
    - in: query
      name: language
      required: false
      description: Reference language
      schema:
        type: string
    - in: query
      name: code
      required: false
      description: Reference code
      schema:
        type: string
    - in: query
      name: displayCode
      required: false
      description: Reference display code
      schema:
        type: string
    - in: query
      name: description
      required: false
      description: Reference description
      schema:
        type: string
    - in: query
      name: sortNo
      required: false
      description: Reference sort number
      schema:
        type: number
    - in: query
      name: helpText
      required: false
      description: Reference help text
      schema:
        type: string
    - in: query
      name: additionalFilters
      required: false
      description: Additional filters
      schema:
        type: string
  responses: {}
