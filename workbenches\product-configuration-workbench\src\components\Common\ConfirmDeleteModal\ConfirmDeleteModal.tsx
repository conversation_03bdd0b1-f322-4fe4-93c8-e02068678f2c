import { Box } from '@mui/material';
import { Button, Modal, styled, Typography } from '@sapiens/insuredpro-ui-component-library';
import { useTranslation } from 'react-i18next';

export interface ConfirmDeleteModalProps {
  deleteHandler?: () => void;
  isModalOpen?: boolean;
  closeHandler?: () => void;
  title: string;
  firstLine?: string;
  secondLine?: string;
}

export const ConfirmDeleteModal = (selectedRowData: ConfirmDeleteModalProps) => {
  const { deleteHandler, isModalOpen, closeHandler, title, firstLine, secondLine } = { ...selectedRowData };
  const { t } = useTranslation();
  const modal = isModalOpen && closeHandler && (
    <StyledModal
      open={isModalOpen}
      title={title}
      closeButtonHandler={closeHandler}
      footerButtons={
        <>
          <Button type="button" onClick={closeHandler} color="secondary" data-testid="delete-modal-cancel-button">
            {t`common.button-cancel`}
          </Button>
          <Button type="button" onClick={deleteHandler} variant="contained" color="primary" data-testid="delete-modal-confirm-button">
            {t`common.button-delete`}
          </Button>
        </>
      }
    >
      <Box sx={{ margin: '16px 20px 16px 25px', height: '72px' }}>
        <Typography variant="default">
          {firstLine}
          <p>{secondLine}</p>
        </Typography>
      </Box>
    </StyledModal>
  );

  return <>{modal}</>;
};

const StyledModal = styled(Modal)`

  & .modal-body {
    width: 90%;
    max-width: 500px;
    color: #000000;
    min-width: 500px;
    height: 260px;
  }
`;
