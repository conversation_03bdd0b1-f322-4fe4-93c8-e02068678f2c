import '../support/commands.ts';

describe('PCW E2E test: CRUD object Ui config all types', () => {
  beforeEach(() => {
    // Login to Keycloak
    cy.loginWithKeycloak();
    // Cypress starts out with a blank slate for each test
    // so we must tell it to visit our website with the `cy.visit()` command.
    cy.visit(Cypress.env('base_url') + '/pcw/landing');
  });

  afterEach(() => {
    //cy.logoutWithKeycloak();
  });

  const config = {
    connectionString: Cypress.env('connectionString'),
    user: Cypress.env('user'),
    password: Cypress.env('password')
  };

  const inputProductLineID = 'G2';
  const inputProductLineIdVersion = '1';

  it('CRUD UI configuration', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();

    //create New UI config
    cy.get('[data-testid="add-new-ui-config-button"]').click();
    cy.wait(1000);
    cy.get('[data-testid="ui-config-name-input"] input').type('type', { force: true });
    cy.get('[data-testid="ui-config-description-input"]').eq(0).type('test description');
    cy.get('[data-testid="ui-config-object-type-select"]').click();
    cy.get('[data-value="G2"]').click();
    cy.get('[data-testid="save-button"]').first().click();
    //cy.get('[data-testid="ui-config-success-alert"]').should('be.visible').contains('Changes were saved.');
    cy.contains('.MuiTypography-root.MuiTypography-medium', 'Success Changes were saved.').should('be.visible');
    cy.wait(1000);
    cy.get('[data-testid="risk-and-objects-tab"]').click();
    cy.get('[data-testid="ui-configuration-tab"]').click();
    cy.filterUIConfig('type');
    //create new UI object block
    cy.get('[data-testid="new-block-button"]').eq(0).click();
    cy.get('[data-testid="block-name-input"]').type('test name2');
    cy.get('[data-testid="block-description-input"]').eq(0).type('test description1');
    cy.get('[data-testid="block-expanded-checkbox"] > :first-child').eq(0).check({ force: true });
    cy.get('[data-testid="block-read-only-checkbox"] > :first-child').eq(0).check({ force: true });
    cy.get('[data-testid="block-sort-no-input"]').eq(0).type('7');
    cy.get('[data-testid="save-button"]').first().click();
    //cy.get('[data-testid="ui-config-success-alert"]').should('be.visible').contains('Changes were saved.');
    cy.contains('.MuiTypography-root.MuiTypography-medium', 'Success Changes were saved.').should('be.visible');
    cy.wait(1000);
    cy.get('[data-testid="risk-and-objects-tab"]').click();
    cy.get('[data-testid="ui-configuration-tab"]').click();
    // navigate to object
    cy.filterUIConfig('type');
    cy.get('[data-testid="block-name-cell-0"] a').first().click();
    // Verify that the button with text "Add field" exists
    cy.contains('button', 'Add field').should('exist');

    /////////////////Create No Label///////////////////////////////////////////////////////////////////
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);
    // select Field type
    cy.get('div[data-testid="field-type"]').click();
    cy.get('div[data-testid="field-type"]').find('div[role="button"]').should('have.attr', 'aria-expanded', 'true');
    cy.get('ul[role="listbox"] li').eq(4).click();
    //select Column name
    cy.selectColumnName('BONUS C07');
    // verify Column Name
    cy.verifyColumnName('BONUS C07');
    // enter value Order field
    cy.enterOrderValue('6');
    // Choose Length
    cy.selectOptionFromDropdown('Large');
    //enter text  display condition
    cy.enterDisplayCondition('test');
    //enter value  rows field
    cy.enterRowsValue('1');
    //check mandatory checkbox
    cy.checkMandatoryCheckbox();
    //check read-only checkbox
    cy.checkReadOnlyCheckbox();
    //expand Default value type dropdown and choose value
    cy.chooseDefaultValueType('Fixed value');
    //enter Default value
    cy.enterDefaultValue('111');
    //choose LOV type
    cy.chooseLOVType('TARIFF_CODE');
    //choose LOV
    cy.chooseLOV('BONUS');
    //enter filtering function
    cy.enterFilteringFunction('Sample');
    //enter dependent column
    cy.enterDependentColumn('test');
    //enter validation rule
    cy.enterValidationRule('Validate');
    //save object UI config
    cy.saveUIConfig();
    cy.wait(5000);
    //verify msg
    cy.verifySuccessMessage();
    //Find the link with the text "Product Line Details" and click it
    cy.contains('a', 'Product Line Details').click();
    // Click on UI config tab
    cy.get('[data-testid="ui-configuration-tab"]').click();
    // navigate to object
    cy.filterUIConfig('type');
    cy.get('[data-testid="block-name-cell-0"] a').first().click();
    cy.wait(1000); // Adjust the wait time if necessary
    // Verify fieds
    cy.verifyFieldType('NOLABEL');
    cy.verifyColumnName('BONUS C07');
    cy.verifyOrderValue('6');
    cy.verifyLengthField('Large');
    cy.verifyDisplayCondition('test');
    cy.verifyRowsValue('1');
    //verify Mandatory checked
    cy.verifyCheckboxChecked('mandatory', true);
    //verify read-only checked
    cy.verifyCheckboxChecked('read-only', true);
    //verify default value type
    cy.verifyDefaultValueType('Fixed value');
    //verify default value
    cy.verifyDefaultValue('111');
    //verify LOV type
    cy.verifyLovType('TARIFF_CODE');
    //verify LOV
    cy.verifyLovValue('BONUS Bonus steps');
    //verify filtering function
    cy.verifyIFilteringfunction('SAMPLE (UF_CONFIG_FUNC)');
    //verify dependent columns
    cy.verifyDependetColumns('TEST');
    //verify validation rule
    cy.verifyValidationRule('Validate mail address');

    /////////////////Create Button///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);
    // select Field type
    cy.get('div[data-testid="field-type"]').click();
    cy.get('div[data-testid="field-type"]').find('div[role="button"]').should('have.attr', 'aria-expanded', 'true');
    cy.get('ul[role="listbox"] li').eq(1).click();
    cy.enterOrderValue('5');
    // Choose Length
    cy.selectOptionFromDropdown('Large');
    //enter text  display condition
    cy.enterDisplayCondition('test1');
    //enter value  rows field
    cy.enterRowsValue('1');
    //check mandatory checkbox
    cy.checkMandatoryCheckbox();
    //check read-only checkbox
    cy.checkReadOnlyCheckbox();
    cy.selectFunction('Button');
    //expand Default value type dropdown and choose value
    cy.chooseDefaultValueType('Fixed value');
    //enter Default value
    cy.enterDefaultValue('111');
    //save object UI config
    cy.saveUIConfig();
    cy.wait(5000);
    //verify msg
    cy.verifySuccessMessage();
    // Find the link with the text "Product Line Details" and click it
    cy.contains('a', 'Product Line Details').click();
    // Click on UI config tab
    cy.get('[data-testid="ui-configuration-tab"]').click();
    // navigate to object
    cy.filterUIConfig('type');
    cy.get('[data-testid="block-name-cell-0"] a').first().click();
    cy.wait(1000); // Adjust the wait time if necessary
    // Verify fieds
    cy.verifyFieldType('BUTTON');
    cy.verifyOrderValue('5');
    cy.verifyLengthField('Large');
    cy.verifyDisplayCondition('test1');
    cy.verifyRowsValue('1');
    //verify Mandatory checked
    cy.verifyCheckboxChecked('mandatory', true);
    //verify read-only checked
    cy.verifyCheckboxChecked('read-only', true);
    //verify Function
    cy.verifyFunction('BUTTON_FILL_C180 (UF_CONFIG_FUNC)');
    //verify default value type
    cy.verifyDefaultValueType('Fixed value');
    //verify default value
    cy.verifyDefaultValue('111');

    ///////////////////////////////Create  Calculated////////////////////////////////////////////////////////////////////////
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);
    // select Field type
    cy.get('div[data-testid="field-type"]').click();
    cy.get('div[data-testid="field-type"]').find('div[role="button"]').should('have.attr', 'aria-expanded', 'true');
    cy.get('ul[role="listbox"] li').eq(2).click();
    // type "Calculated" into Column name
    cy.get('div[data-testid="column-name"] input[type="text"]').type('Calculated');
    // enter value Order field
    cy.enterOrderValue('4');
    // Choose Length
    cy.selectOptionFromDropdown('Large');
    //enter text  display condition
    cy.enterDisplayCondition('test');
    //enter value  rows field
    cy.enterRowsValue('1');
    //check mandatory checkbox
    cy.checkMandatoryCheckbox();
    //check read-only checkbox
    cy.checkReadOnlyCheckbox();
    cy.selectFunction('Button');
    cy.saveUIConfig();
    cy.wait(5000);
    //verify msg
    cy.verifySuccessMessage();
    //Find the link with the text "Product Line Details" and click it
    cy.contains('a', 'Product Line Details').click();
    // Click on UI config tab
    cy.get('[data-testid="ui-configuration-tab"]').click();
    // navigate to object
    cy.filterUIConfig('type');
    cy.get('[data-testid="block-name-cell-0"] a').first().click();
    cy.wait(1000); // Adjust the wait time if necessary
    // Verify fieds
    cy.verifyFieldType('CALCULATED');
    cy.get('div[data-testid="column-name"] input').should('have.value', 'CALCULATED');
    cy.verifyOrderValue('4');
    cy.verifyLengthField('Large');
    cy.verifyDisplayCondition('test');
    cy.verifyRowsValue('1');
    //verify Mandatory checked
    cy.verifyCheckboxChecked('mandatory', true);
    //verify read-only checked
    cy.verifyCheckboxChecked('read-only', true);
    //verify Function
    cy.verifyFunction('BUTTON_FILL_C180 (UF_CONFIG_FUNC)');

    ////////////////Create Placeholder///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);
    // select Field type
    cy.get('div[data-testid="field-type"]').click();
    cy.get('div[data-testid="field-type"]').find('div[role="button"]').should('have.attr', 'aria-expanded', 'true');
    cy.get('ul[role="listbox"] li').eq(5).click();
    cy.enterOrderValue('3');
    //enter text  display condition
    cy.enterDisplayCondition('test1');
    //enter value  rows field
    cy.enterRowsValue('1');
    //check mandatory checkbox
    cy.checkMandatoryCheckbox();
    //check read-only checkbox
    cy.checkReadOnlyCheckbox();
    cy.chooseDefaultValueType('Fixed value');
    //enter Default value
    cy.enterDefaultValue('111');
    cy.chooseLOVType('TARIFF_CODE');
    //choose LOV
    cy.chooseLOV('BONUS');
    //enter filtering function
    cy.enterFilteringFunction('Sample');
    //enter dependent column
    cy.enterDependentColumn('test');
    //enter validation rule
    cy.enterValidationRule('Validate');
    //save object UI config
    cy.saveUIConfig();
    cy.wait(5000);
    //verify msg
    cy.verifySuccessMessage();
    // Find the link with the text "Product Line Details" and click it
    cy.contains('a', 'Product Line Details').click();
    // Click on UI config tab
    cy.get('[data-testid="ui-configuration-tab"]').click();
    // navigate to object
    cy.filterUIConfig('type');
    cy.get('[data-testid="block-name-cell-0"] a').first().click();
    cy.wait(1000); // Adjust the wait time if necessary
    // Verify fieds
    cy.verifyFieldType('PLCHOLDER');
    cy.verifyOrderValue('3');
    cy.verifyDisplayCondition('test1');
    cy.verifyRowsValue('1');
    //verify Mandatory checked
    cy.verifyCheckboxChecked('mandatory', true);
    //verify read-only checked
    cy.verifyCheckboxChecked('read-only', true);
    //verify default value type
    cy.verifyDefaultValueType('Fixed value');
    //verify default value
    cy.verifyDefaultValue('111');
    cy.verifyLovType('TARIFF_CODE');
    //verify LOV
    cy.verifyLovValue('BONUS Bonus steps');
    //verify filtering function
    cy.verifyIFilteringfunction('SAMPLE (UF_CONFIG_FUNC)');
    //verify dependent columns
    cy.verifyDependetColumns('TEST');
    //verify validation rule
    cy.verifyValidationRule('Validate mail address');

    ////////////////Create No label///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);
    // select Field type
    cy.get('div[data-testid="field-type"]').click();
    cy.get('div[data-testid="field-type"]').find('div[role="button"]').should('have.attr', 'aria-expanded', 'true');
    cy.get('ul[role="listbox"] li').eq(4).click();
    //select Column name
    cy.selectColumnName('CANCEL_CODE');
    cy.enterOrderValue('2');
    cy.selectOptionFromDropdown('Large');
    //enter text  display condition
    cy.enterDisplayCondition('test1');
    //enter value  rows field
    cy.enterRowsValue('1');
    //check mandatory checkbox
    cy.checkMandatoryCheckbox();
    //check read-only checkbox
    cy.checkReadOnlyCheckbox();
    cy.chooseDefaultValueType('Fixed value');
    //enter Default value
    cy.enterDefaultValue('111');
    cy.chooseLOVType('TARIFF_CODE');
    //choose LOV
    cy.chooseLOV('BONUS');
    //enter filtering function
    cy.enterFilteringFunction('Sample');
    //enter dependent column
    cy.enterDependentColumn('test');
    //enter validation rule
    cy.enterValidationRule('Validate');
    //save object UI config
    cy.saveUIConfig();
    cy.wait(5000);
    //verify msg
    cy.verifySuccessMessage();
    // Find the link with the text "Product Line Details" and click it
    cy.contains('a', 'Product Line Details').click();
    // Click on UI config tab
    cy.get('[data-testid="ui-configuration-tab"]').click();
    // navigate to object
    cy.filterUIConfig('type');
    cy.get('[data-testid="block-name-cell-0"] a').first().click();
    cy.wait(1000); // Adjust the wait time if necessary
    // Verify fieds
    cy.verifyFieldType('NOLABEL');
    cy.verifyColumnName('CANCEL_CODE');
    cy.verifyOrderValue('2');
    cy.verifyLengthField('Large');
    cy.verifyDisplayCondition('test1');
    cy.verifyRowsValue('1');
    //verify Mandatory checked
    cy.verifyCheckboxChecked('mandatory', true);
    //verify read-only checked
    cy.verifyCheckboxChecked('read-only', true);
    //verify default value type
    cy.verifyDefaultValueType('Fixed value');
    //verify default value
    cy.verifyDefaultValue('111');
    cy.verifyLovType('TARIFF_CODE');
    //verify LOV
    cy.verifyLovValue('BONUS Bonus steps');
    //verify filtering function
    cy.verifyIFilteringfunction('SAMPLE (UF_CONFIG_FUNC)');
    //verify dependent columns
    cy.verifyDependetColumns('TEST');
    //verify validation rule
    cy.verifyValidationRule('Validate mail address');

    //delete Object UI config field///////////////////////////////////////////////////////
    // Find the link with the text "Product Line Details" and click it
    cy.contains('a', 'Product Line Details').click();
    // Click on UI config tab
    cy.get('[data-testid="ui-configuration-tab"]').click();
    // navigate to object
    cy.filterUIConfig('type');
    //delete UI config
    cy.get('[data-testid="DeleteIcon"]').eq(0).click();
    cy.get('[data-testid="delete-modal-confirm-button"]').click();
    //click on approve deletion
    //cy.get('[data-testid="ui-config-success-alert"]').should('be.visible').contains('success');
    cy.get('.MuiTypography-medium').should('contain.text', 'Success Data was deleted successfully.').should('be.visible');
  });
});
