import '../support/commands.ts';

describe('PCW E2E test: field_type_CALCULATED_validations', () => {
  beforeEach(() => {
    // Login to Keycloak
    cy.loginWithKeycloak();
    // Cypress starts out with a blank slate for each test
    // so we must tell it to visit our website with the `cy.visit()` command.
    cy.visit(Cypress.env('base_url') + '/pcw/landing');
  });

  afterEach(() => {
    //cy.logoutWithKeycloak();
  });

  const config = {
    connectionString: Cypress.env('connectionString'),
    user: Cypress.env('user'),
    password: Cypress.env('password')
  };

  const inputProductLineID = 'G2';
  const inputProductLineIdVersion = '1';

  it('CALCULATED read-only fields', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();
    cy.filterUIConfig('ADF_G2');
    cy.get('[data-testid="block-name-cell-1"] a').first().click();
    //create new CALCULATED field without any data
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);
    // select Field type
    cy.get('div[data-testid="field-type"]').click();
    cy.get('div[data-testid="field-type"]').find('div[role="button"]').should('have.attr', 'aria-expanded', 'true');
    cy.get('ul[role="listbox"] li').eq(2).click();
    //check read-only fields
    cy.get('#default-value-dropdown-searchable').should('be.disabled');
    cy.get('[data-testid="default-value-type"] div[role="button"]').should('have.attr', 'aria-disabled', 'true');
    cy.get('div[role="button"][aria-expanded="false"][aria-haspopup="listbox"][aria-labelledby="inputLabel"][aria-disabled="true"]').should(
      'have.attr',
      'aria-disabled',
      'true'
    );
    cy.get('input#lov-dropdown-searchable').should('be.disabled');
    cy.get('input#filtering-function-dropdown-searchable').should('be.disabled');
    cy.get('div[data-testid="dependent-columns"] input').should('be.disabled');
    cy.get('#validation-rule-dropdown-searchable').should('be.disabled');
  });

  it('CALCULATED field without any data', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();
    cy.filterUIConfig('ADF_G2');
    cy.get('[data-testid="block-name-cell-1"] a').first().click();
    //create new CALCULATED field without any data
    cy.wait(1000);
    cy.get('[data-testid="AddCircleIcon"]').click();
    cy.wait(1000);
    // select Field type
    cy.get('div[data-testid="field-type"]').click();
    cy.get('div[data-testid="field-type"]').find('div[role="button"]').should('have.attr', 'aria-expanded', 'true');
    cy.get('ul[role="listbox"] li').eq(2).click();
    cy.saveUIConfig();
    cy.wait(1000);
    // Verify fieds
    cy.verifyFieldType('CALCULATED');

    //Check Column Name
    cy.get('[data-testid="column-name"]').parent().parent().siblings('span').should('be.visible').and('contain', 'Required field');
    // check Order
    cy.get('[data-testid="sort-no"]').parent().parent().siblings('span').should('be.visible').and('contain', 'Required field');
    //Check Length
    cy.get('[data-testid="length"]').parent().parent().siblings('span').should('be.visible').and('contain', 'Required field');
    // check function required field
    /* cy.get('[data-testid="function-dropdown-searchable"]')
    .parent()
    .parent() 
    .siblings('span') 
    .should('be.visible')
    .and('contain', 'Required field');
  */
    cy.contains('span', 'Required field').should('be.visible');
    //Check ErrorIcon is shown
    cy.get('[data-testid="ErrorIcon"]').should('be.visible');
  });
});
