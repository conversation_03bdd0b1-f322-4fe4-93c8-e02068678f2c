path: /v1/ui-configuration-fields
method: get
flow: pcwGetUiConfigurationFields.yaml
secured: false
errorHandlers:
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
  - PCW_BAD_REQUEST.yaml
definition:
  operationId: pcwGetUiConfigurationFields
  tags:
    - pcw
  summary: ""
  description: ""
  parameters:
    - in: query
      name: productLineId
      required: true
      description: Product Line Id to search for.
      schema:
        type: string
    - in: query
      name: productLineVersion
      required: true
      description: Product Line Version to search for.
      schema:
        type: number
    - in: query
      name: uiConfigurationName
      required: true
      description: Name of Product Line UI Configuration to search for.
      schema:
        type: string
    - in: query
      name: uiConfigurationBlockName
      required: true
      description: Name of Product Line UI Configuration Block to search for.
      schema:
        type: string
    - in: query
      name: columnName
      required: false
      description: Column name to search for. Partial search supported.
      schema:
        type: string
    - in: query
      name: columnLabel
      required: false
      description: Column label to search for. Partial search supported.
      schema:
        type: string
    - in: query
      name: itemName
      required: false
      description: Item name to search for. Partial search supported.
      schema:
        type: string
    - in: query
      name: dataType
      required: false
      description: Data type to search for.
      schema:
        type: string
    - in: query
      name: sortNo
      required: false
      description: Sort number to search for. Partial search supported.
      schema:
        type: number
    - in: query
      name: length
      required: false
      description: Length to search for.
      schema:
        type: string
    - in: query
      name: numberFormatDescription
      required: false
      description: Description of number format to search for. Partial search supported.
      schema:
        type: string
    - in: query
      name: sort
      required: false
      description: "Format: property(:asc|desc). Default order is ascending. If no
        sort parameter is provided, data is sorted by sortNo in ascending
        order.  Usage example: ...?sort=columnName:desc  columnName,
        columnLabel, dataType (RISK fields do not have dataType), sortNo, length
        and numberFormatDescription (applies only to RISK configuration
        fields)."
      schema:
        type: string
    - in: query
      name: page
      required: false
      description: "Result page you want to retrieve (1..N)  Example : 1"
      schema:
        type: number
    - in: query
      name: size
      required: false
      description: "Number of records per page  Example : 25"
      schema:
        type: number
    - in: query
      name: fieldType
      required: false
      description: Field type to search for.
      schema:
        type: string
    - in: query
      name: id
      required: false
      description: Field ID (UIX_SEQ_NO).
      schema:
        type: number
  responses: {}
