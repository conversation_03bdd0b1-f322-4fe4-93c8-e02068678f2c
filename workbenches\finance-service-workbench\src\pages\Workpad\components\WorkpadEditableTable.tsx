import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import EditIcon from '@mui/icons-material/Edit';
import { Avatar, Button } from '@sapiens/insuredpro-ui-component-library';
import { DynamicType, useTranslation } from '@sapiens/workbench-feature-library';
import { CellContext, ColumnDef, Row } from '@tanstack/react-table';
import React from 'react';

import { SingleRowEditProvider, SingleRowEditTable, useSingleRowEdit, useEditableField } from '@/components/Common/Table';
import { Workpads } from '@/models/workpads.model';
import { FSW_WORKPAD_BASE_URL } from '@/services/workpad/workpad.service';

// Editable Cell Component
const EditableCell: React.FC<{
  cellContext: CellContext<DynamicType, unknown>;
  fieldName: string;
  type?: 'text' | 'number';
  placeholder?: string;
}> = ({ cellContext, fieldName, type = 'text', placeholder }) => {
  const isEditable = useEditableField(cellContext);
  const value = cellContext.getValue() as string | number;

  if (!isEditable) {
    return <span>{value || '-'}</span>;
  }

  // For now, use simple input fields until form context is properly set up
  if (type === 'number') {
    return (
      <input
        type="number"
        defaultValue={value as number}
        placeholder={placeholder}
        style={{
          width: '100%',
          padding: '8px',
          border: '1px solid #ccc',
          borderRadius: '4px',
          fontSize: '14px'
        }}
      />
    );
  }

  return (
    <input
      type="text"
      defaultValue={value as string}
      placeholder={placeholder}
      style={{
        width: '100%',
        padding: '8px',
        border: '1px solid #ccc',
        borderRadius: '4px',
        fontSize: '14px'
      }}
    />
  );
};

// Action Buttons Component
const ActionButtons: React.FC<{
  cellContext: CellContext<DynamicType, unknown>;
  onDuplicate?: (workpadNo: number) => void;
}> = ({ cellContext, onDuplicate }) => {
  const { setEditMode, clearEditMode } = useSingleRowEdit();
  const isEditable = useEditableField(cellContext);
  const rowId = cellContext.row.id;
  const workpadNo = cellContext.row.original.workpadNo;

  const handleDuplicate = () => {
    if (onDuplicate && workpadNo) {
      onDuplicate(workpadNo);
    }
  };

  if (isEditable) {
    return (
      <div style={{ display: 'flex', gap: '8px' }}>
        <Button variant="text" onClick={() => clearEditMode()} title="Save">
          <CheckIcon />
        </Button>
        <Button variant="text" onClick={() => clearEditMode()} title="Cancel">
          <CloseIcon />
        </Button>
      </div>
    );
  }

  return (
    <div style={{ display: 'flex', gap: '8px' }}>
      <Button variant="text" onClick={() => setEditMode(rowId)} title="Edit">
        <EditIcon />
      </Button>
      <Button variant="text" onClick={handleDuplicate} title="Duplicate">
        <ContentCopyIcon />
      </Button>
    </div>
  );
};

// Workpad Columns Definition
const useWorkpadEditableColumns = (onDuplicate?: (workpadNo: number) => void): ColumnDef<Workpads>[] => {
  const { t } = useTranslation('fsw');

  return [
    {
      accessorKey: 'workpadNo',
      id: 'workpadNo',
      header: t('workpad.workpadNo') ?? 'Workpad No.',
      cell: ({ row }) => row.original.workpadNo
    },
    {
      accessorKey: 'workpadType',
      id: 'workpadType',
      header: t('workpad.workpadType') ?? 'Workpad Type',
      cell: (cellContext) => (
        <EditableCell
          cellContext={cellContext}
          fieldName="workpadType"
          type="number"
          placeholder="Enter type"
        />
      )
    },
    {
      accessorKey: 'workpadTitle',
      id: 'workpadTitle',
      header: t('workpad.workpadTitle') ?? 'Workpad Title',
      cell: (cellContext) => (
        <EditableCell
          cellContext={cellContext}
          fieldName="workpadTitle"
          placeholder="Enter title"
        />
      )
    },
    {
      accessorKey: 'closeDate',
      id: 'closeDate',
      header: t('workpad.postDate') ?? 'Post Date',
      cell: ({ row }) => row.original.closeDate || '-'
    },
    {
      accessorKey: 'status',
      id: 'status',
      header: t('workpad.workpadStatus') ?? 'Status',
      cell: ({ row }) => row.original.status || 'New'
    },
    {
      accessorKey: 'changedBy',
      id: 'changedBy',
      header: t('workpad.changedBy') ?? 'Changed By',
      cell: ({ row }) => {
        if (!row.original.changedBy) return '-';
        return (
          <Avatar color="tertiary" size="md">
            {row.original.changedBy}
          </Avatar>
        );
      }
    },
    {
      accessorKey: 'actions',
      id: 'actions',
      header: '',
      cell: (cellContext) => <ActionButtons cellContext={cellContext} onDuplicate={onDuplicate} />
    }
  ];
};

// Main Workpad Editable Table Component
interface WorkpadEditableTableProps {
  onRowDoubleClick?: (row: Row<DynamicType>) => void;
  onRowSelection?: (row: Row<DynamicType>) => void;
  onDuplicate?: (workpadNo: number) => void;
}

const WorkpadEditableTable: React.FC<WorkpadEditableTableProps> = ({
  onRowDoubleClick,
  onRowSelection,
  onDuplicate
}) => {
  const { t } = useTranslation('fsw');
  const columns = useWorkpadEditableColumns(onDuplicate);

  const compareRowIds = (id1: string, id2: string) => id1 === id2;

  const tableOptions = {
    showToolbar: true,
    showColumnFilters: true,
    manualSorting: true,
    manualFiltering: true,
    enableRowSelection: true,
    noRecordsText: t('workpad.noRecordsText') || 'No workpads found',
    errorText: t('workpad.errorText') || 'Error loading workpads'
  };

  return (
    <SingleRowEditProvider compareRowIds={compareRowIds}>
      <SingleRowEditTable
        columns={columns}
        url={FSW_WORKPAD_BASE_URL}
        tableId="fsw-workpad-editable-table"
        options={tableOptions}
        enableDoubleClickEdit={true}
        onRowDoubleClick={onRowDoubleClick}
        onRowSelection={onRowSelection}
        addNewRowConfig={{
          newButtonLabel: t('workpad.createWorkpad') || 'Create Workpad'
        }}
      />
    </SingleRowEditProvider>
  );
};

export default WorkpadEditableTable;
