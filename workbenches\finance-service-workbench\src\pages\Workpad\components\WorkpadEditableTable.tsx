import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import { Avatar, Button, Box } from '@sapiens/insuredpro-ui-component-library';
import { DataTable, DynamicType, useTranslation } from '@sapiens/workbench-feature-library';
import { CellContext, ColumnDef, Row } from '@tanstack/react-table';
import React, { useState, useMemo } from 'react';

import { SingleRowEditProvider, useSingleRowEdit, useEditableField } from '@/components/Common/Table';
import { Workpads } from '@/models/workpads.model';
import { useGetWorkpads } from '@/services/workpad/workpad.service';

// Editable Cell Component
const EditableCell: React.FC<{
  cellContext: CellContext<DynamicType, unknown>;
  fieldName: string;
  type?: 'text' | 'number';
  placeholder?: string;
}> = ({ cellContext, fieldName, type = 'text', placeholder }) => {
  const isEditable = useEditableField(cellContext);
  const value = cellContext.getValue() as string | number;

  if (!isEditable) {
    return <span>{value || '-'}</span>;
  }

  // For now, use simple input fields until form context is properly set up
  if (type === 'number') {
    return (
      <input
        type="number"
        defaultValue={value as number}
        placeholder={placeholder}
        style={{
          width: '100%',
          padding: '8px',
          border: '1px solid #ccc',
          borderRadius: '4px',
          fontSize: '14px'
        }}
      />
    );
  }

  return (
    <input
      type="text"
      defaultValue={value as string}
      placeholder={placeholder}
      style={{
        width: '100%',
        padding: '8px',
        border: '1px solid #ccc',
        borderRadius: '4px',
        fontSize: '14px'
      }}
    />
  );
};

// Action Buttons Component
const ActionButtons: React.FC<{
  cellContext: CellContext<DynamicType, unknown>;
  onDuplicate?: (workpadNo: number) => void;
  onSaveNew?: (data: Partial<Workpads>) => void;
  onCancelNew?: () => void;
}> = ({ cellContext, onDuplicate, onSaveNew, onCancelNew }) => {
  const { setEditMode, clearEditMode } = useSingleRowEdit();
  const isEditable = useEditableField(cellContext);
  const rowId = cellContext.row.id;
  const workpadNo = cellContext.row.original.workpadNo;
  const isNewRow = cellContext.row.original.newRowId;

  const handleDuplicate = () => {
    if (onDuplicate && workpadNo) {
      onDuplicate(workpadNo);
    }
  };

  const handleSave = () => {
    if (isNewRow && onSaveNew) {
      // For new rows, call the save new handler
      onSaveNew(cellContext.row.original);
    } else {
      // For existing rows, just clear edit mode
      clearEditMode();
    }
  };

  const handleCancel = () => {
    if (isNewRow && onCancelNew) {
      // For new rows, call the cancel new handler
      onCancelNew();
    } else {
      // For existing rows, just clear edit mode
      clearEditMode();
    }
  };

  if (isEditable) {
    return (
      <div style={{ display: 'flex', gap: '8px' }}>
        <Button variant="text" onClick={handleSave} title="Save">
          <CheckIcon />
        </Button>
        <Button variant="text" onClick={handleCancel} title="Cancel">
          <CloseIcon />
        </Button>
      </div>
    );
  }

  // Don't show edit/duplicate buttons for new unsaved rows
  if (isNewRow) {
    return null;
  }

  return (
    <div style={{ display: 'flex', gap: '8px' }}>
      <Button variant="text" onClick={() => setEditMode(rowId)} title="Edit">
        <EditIcon />
      </Button>
      <Button variant="text" onClick={handleDuplicate} title="Duplicate">
        <ContentCopyIcon />
      </Button>
    </div>
  );
};

// Workpad Columns Definition
const useWorkpadEditableColumns = (
  onDuplicate?: (workpadNo: number) => void,
  onSaveNew?: (data: Partial<Workpads>) => void,
  onCancelNew?: () => void
): ColumnDef<Workpads>[] => {
  const { t } = useTranslation('fsw');

  return [
    {
      accessorKey: 'workpadNo',
      id: 'workpadNo',
      header: t('workpad.workpadNo') ?? 'Workpad No.',
      cell: ({ row }) => row.original.workpadNo
    },
    {
      accessorKey: 'workpadType',
      id: 'workpadType',
      header: t('workpad.workpadType') ?? 'Workpad Type',
      cell: (cellContext) => (
        <EditableCell
          cellContext={cellContext}
          fieldName="workpadType"
          type="number"
          placeholder="Enter type"
        />
      )
    },
    {
      accessorKey: 'workpadTitle',
      id: 'workpadTitle',
      header: t('workpad.workpadTitle') ?? 'Workpad Title',
      cell: (cellContext) => (
        <EditableCell
          cellContext={cellContext}
          fieldName="workpadTitle"
          placeholder="Enter title"
        />
      )
    },
    {
      accessorKey: 'closeDate',
      id: 'closeDate',
      header: t('workpad.postDate') ?? 'Post Date',
      cell: ({ row }) => row.original.closeDate || '-'
    },
    {
      accessorKey: 'status',
      id: 'status',
      header: t('workpad.workpadStatus') ?? 'Status',
      cell: ({ row }) => row.original.status || 'New'
    },
    {
      accessorKey: 'changedBy',
      id: 'changedBy',
      header: t('workpad.changedBy') ?? 'Changed By',
      cell: ({ row }) => {
        if (!row.original.changedBy) return '-';
        return (
          <Avatar color="tertiary" size="md">
            {row.original.changedBy}
          </Avatar>
        );
      }
    },
    {
      accessorKey: 'actions',
      id: 'actions',
      header: '',
      cell: (cellContext) => (
        <ActionButtons
          cellContext={cellContext}
          onDuplicate={onDuplicate}
          onSaveNew={onSaveNew}
          onCancelNew={onCancelNew}
        />
      )
    }
  ];
};

// Main Workpad Editable Table Component
interface WorkpadEditableTableProps {
  onRowSelection?: (row: Row<DynamicType>) => void;
  onDuplicate?: (workpadNo: number) => void;
  onCreateWorkpad?: () => void;
  onSave?: () => void;
  onPostItems?: () => void;
}

// Inner component that uses the SingleRowEdit context
const WorkpadEditableTableInner: React.FC<WorkpadEditableTableProps> = ({
  onRowSelection,
  onDuplicate,
  onCreateWorkpad,
  onSave,
  onPostItems
}) => {
  const { t } = useTranslation('fsw');
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});
  const [isCreatingNew, setIsCreatingNew] = useState(false);
  const [newWorkpadData, setNewWorkpadData] = useState<Partial<Workpads> | null>(null);
  const columns = useWorkpadEditableColumns(onDuplicate, handleSaveNewWorkpad, handleCancelNewWorkpad);
  const { setEditMode, clearEditMode } = useSingleRowEdit();

  // Fetch workpad data using the existing service
  const { data, isLoading, isError } = useGetWorkpads({
    page: 1,
    size: 1000 // Fetch all records for now
  });

  // Simple row click handler for selection
  const handleRowClick = (row: Row<Workpads>) => {
    // Single click - select row
    const newSelection = { [row.id]: !rowSelection[row.id] };
    setRowSelection(newSelection);
    onRowSelection?.(row as Row<DynamicType>);
  };

  // Handle creating new workpad
  const handleCreateNewWorkpad = () => {
    if (isCreatingNew) return; // Prevent multiple new rows

    const newWorkpad: Partial<Workpads> = {
      workpadNo: 0, // Temporary ID for new row
      workpadType: 1,
      workpadTitle: '',
      closeDate: '',
      checkSumAmt: 0,
      status: 'New',
      changedBy: '',
      newRowId: 'new-workpad' // Special identifier for new rows
    };

    setNewWorkpadData(newWorkpad);
    setIsCreatingNew(true);

    // Enter edit mode for the new row
    setTimeout(() => {
      setEditMode(undefined, 'new-workpad');
    }, 100);

    onCreateWorkpad?.();
  };

  // Handle saving new workpad
  const handleSaveNewWorkpad = (data: Partial<Workpads>) => {
    console.log('💾 Saving new workpad:', data);
    // Here you would call the API to create the workpad
    // For now, just clear the new row state
    setIsCreatingNew(false);
    setNewWorkpadData(null);
    clearEditMode();
  };

  // Handle canceling new workpad
  const handleCancelNewWorkpad = () => {
    console.log('❌ Canceling new workpad creation');
    setIsCreatingNew(false);
    setNewWorkpadData(null);
    clearEditMode();
  };

  const tableData = useMemo(() => {
    const existingData = data?.content || [];
    if (isCreatingNew && newWorkpadData) {
      return [newWorkpadData as Workpads, ...existingData];
    }
    return existingData;
  }, [data?.content, isCreatingNew, newWorkpadData]);

  // Toolbar buttons
  const toolbarButtons = (
    <Box display="flex" gap="10px">
      <Button
        spacing="tight"
        color="secondary"
        onClick={handleCreateNewWorkpad}
        disabled={isCreatingNew}
      >
        {t('workpad.createWorkpad') || 'Create Workpad'}
      </Button>
      <Button
        spacing="tight"
        color="secondary"
        startIcon={<SaveIcon />}
        onClick={onSave}
      >
        {t('workpad.save') || 'Save'}
      </Button>
      <Button
        spacing="tight"
        color="primary"
        onClick={onPostItems}
      >
        {t('workpad.postItems') || 'Post Items'}
      </Button>
    </Box>
  );

  return (
    <DataTable<Workpads, unknown>
      columns={columns}
      data={tableData}
      tableId="fsw-workpad-editable-table"
      showPagination={false}
      showToolbar={true}
      showColumnFilters={true}
      enableRowSelection={true}
      rowSelection={rowSelection}
      setRowSelection={setRowSelection}
      onRowClick={handleRowClick}
      rightToolbarSlot={toolbarButtons}
      tableStatusIndicators={{
        isLoading,
        isError,
        errorText: t('workpad.errorText') || 'Error loading workpads',
        noRecordsText: t('workpad.noRecordsText') || 'No workpads found'
      }}
    />
  );
};

// Main component that provides the context
const WorkpadEditableTable: React.FC<WorkpadEditableTableProps> = (props) => {
  const compareRowIds = (id1: string, id2: string) => id1 === id2;

  return (
    <SingleRowEditProvider compareRowIds={compareRowIds}>
      <WorkpadEditableTableInner {...props} />
    </SingleRowEditProvider>
  );
};

export default WorkpadEditableTable;
