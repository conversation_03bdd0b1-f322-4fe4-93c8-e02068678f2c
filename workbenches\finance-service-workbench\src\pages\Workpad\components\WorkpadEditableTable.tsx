import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import { Avatar, Button, Box } from '@sapiens/insuredpro-ui-component-library';
import { DataTable, DynamicType, useTranslation } from '@sapiens/workbench-feature-library';
import { CellContext, ColumnDef, Row } from '@tanstack/react-table';
import React, { useState, useMemo, useEffect, useRef, useCallback } from 'react';

import { SingleRowEditProvider, useSingleRowEdit, useEditableField } from '@/components/Common/Table';
import { Workpads } from '@/models/workpads.model';
import { useGetWorkpads, useCreateWorkpad, useUpdateWorkpad } from '@/services/workpad/workpad.service';

// Bulletproof input that uses direct DOM manipulation to avoid React re-renders
const BulletproofInput: React.FC<{
  initialValue: string | number;
  type?: 'text' | 'number';
  placeholder?: string;
  fieldName: string;
  rowId: string;
  onValueChange: (value: string | number) => void;
}> = ({ initialValue, type = 'text', placeholder, fieldName, rowId, onValueChange }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const inputElementRef = useRef<HTMLInputElement | null>(null);
  const isSetupRef = useRef(false);

  useEffect(() => {
    if (containerRef.current && !isSetupRef.current) {
      // Create input element directly in DOM
      const input = document.createElement('input');
      input.type = type;
      input.value = String(initialValue);
      input.placeholder = placeholder || '';
      input.style.cssText = `
        width: 100%;
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
        fontSize: 14px;
        outline: none;
        box-sizing: border-box;
      `;

      // Add unique identifier
      input.setAttribute('data-field', fieldName);
      input.setAttribute('data-row', rowId);

      // Add event listener directly to DOM element
      const handleChange = (e: Event) => {
        const target = e.target as HTMLInputElement;
        const newValue = target.value;
        if (type === 'number') {
          onValueChange(parseInt(newValue) || 0);
        } else {
          onValueChange(newValue);
        }
      };

      input.addEventListener('input', handleChange);

      // Append to container
      containerRef.current.appendChild(input);
      inputElementRef.current = input;

      // Focus and select
      setTimeout(() => {
        input.focus();
        input.select();
      }, 0);

      isSetupRef.current = true;

      // Cleanup function
      return () => {
        if (input.parentNode) {
          input.removeEventListener('input', handleChange);
          input.parentNode.removeChild(input);
        }
      };
    }
  }, []); // Only run once, never again!

  // This component never re-renders the input because it's created outside React
  return <div ref={containerRef} />;
};

// Completely isolated editable cell that doesn't use context
const EditableCell: React.FC<{
  cellContext: CellContext<DynamicType, unknown>;
  fieldName: string;
  type?: 'text' | 'number';
  placeholder?: string;
  isEditable?: boolean; // Pass edit state as prop instead of using context
}> = React.memo(({ cellContext, fieldName, type = 'text', placeholder, isEditable = false }) => {
  const value = cellContext.getValue() as string | number;

  if (!isEditable) {
    return <span>{value || '-'}</span>;
  }

  const handleValueChange = (newValue: string | number) => {
    const rowData = cellContext.row.original;
    rowData[fieldName] = newValue;
  };

  // Use a stable key that doesn't change during typing
  const stableKey = `${cellContext.row.id}-${fieldName}-edit`;

  return (
    <BulletproofInput
      key={stableKey}
      initialValue={value}
      type={type}
      placeholder={placeholder}
      fieldName={fieldName}
      rowId={cellContext.row.id}
      onValueChange={handleValueChange}
    />
  );
}, (prevProps, nextProps) => {
  // Only re-render if essential props change
  return (
    prevProps.cellContext.row.id === nextProps.cellContext.row.id &&
    prevProps.fieldName === nextProps.fieldName &&
    prevProps.isEditable === nextProps.isEditable
  );
});

// Action Buttons Component
const ActionButtons: React.FC<{
  cellContext: CellContext<DynamicType, unknown>;
  onDuplicate?: (workpadNo: number) => void;
  onSaveNew?: (data: Partial<Workpads>) => void;
  onCancelNew?: () => void;
  onSaveExisting?: (data: Partial<Workpads>) => void;
  isEditable?: boolean;
  setEditMode?: (rowId?: string, newRowId?: string) => void;
  clearEditMode?: () => void;
}> = ({ cellContext, onDuplicate, onSaveNew, onCancelNew, onSaveExisting, isEditable = false, setEditMode, clearEditMode }) => {
  const rowId = cellContext.row.id;
  const workpadNo = cellContext.row.original.workpadNo;
  const isNewRow = cellContext.row.original.newRowId;

  const handleDuplicate = () => {
    if (onDuplicate && workpadNo) {
      onDuplicate(workpadNo);
    }
  };

  const handleSave = () => {
    // Collect current values from DOM inputs
    const currentData = { ...cellContext.row.original };

    // Find all inputs for this row
    const rowInputs = document.querySelectorAll(`input[data-row="${cellContext.row.id}"]`);
    rowInputs.forEach((input) => {
      const htmlInput = input as HTMLInputElement;
      const fieldName = htmlInput.getAttribute('data-field');
      if (fieldName) {
        if (htmlInput.type === 'number') {
          currentData[fieldName] = parseInt(htmlInput.value) || 0;
        } else {
          currentData[fieldName] = htmlInput.value;
        }
      }
    });

    if (isNewRow && onSaveNew) {
      // For new rows, call the save new handler
      onSaveNew(currentData);
    } else if (onSaveExisting) {
      // For existing rows, call the save existing handler
      onSaveExisting(currentData);
    } else if (clearEditMode) {
      // Fallback: just clear edit mode
      clearEditMode();
    }
  };

  const handleCancel = () => {
    if (isNewRow && onCancelNew) {
      // For new rows, call the cancel new handler
      onCancelNew();
    } else if (clearEditMode) {
      // For existing rows, just clear edit mode
      clearEditMode();
    }
  };

  if (isEditable) {
    return (
      <div style={{ display: 'flex', gap: '8px' }}>
        <Button variant="text" onClick={handleSave} title="Save">
          <CheckIcon />
        </Button>
        <Button variant="text" onClick={handleCancel} title="Cancel">
          <CloseIcon />
        </Button>
      </div>
    );
  }

  // Don't show edit/duplicate buttons for new unsaved rows
  if (isNewRow) {
    return null;
  }

  return (
    <div style={{ display: 'flex', gap: '8px' }}>
      <Button variant="text" onClick={() => setEditMode?.(rowId)} title="Edit">
        <EditIcon />
      </Button>
      <Button variant="text" onClick={handleDuplicate} title="Duplicate">
        <ContentCopyIcon />
      </Button>
    </div>
  );
};

// Workpad Columns Definition
const useWorkpadEditableColumns = (
  onDuplicate?: (workpadNo: number) => void,
  onSaveNew?: (data: Partial<Workpads>) => void,
  onCancelNew?: () => void,
  onSaveExisting?: (data: Partial<Workpads>) => void,
  getEditMode?: (rowId?: string, newRowId?: string) => boolean,
  setEditMode?: (rowId?: string, newRowId?: string) => void,
  clearEditMode?: () => void
): ColumnDef<Workpads>[] => {
  const { t } = useTranslation('fsw');

  return [
    {
      accessorKey: 'workpadNo',
      id: 'workpadNo',
      header: t('workpad.workpadNo') ?? 'Workpad No.',
      cell: ({ row }) => row.original.workpadNo
    },
    {
      accessorKey: 'workpadType',
      id: 'workpadType',
      header: t('workpad.workpadType') ?? 'Workpad Type',
      cell: (cellContext) => {
        const rowId = cellContext.row.id;
        const isNewRow = cellContext.row.original.newRowId;
        const isEditable = getEditMode ? (isNewRow ? getEditMode(undefined, isNewRow) : getEditMode(rowId)) : false;

        return (
          <EditableCell
            cellContext={cellContext}
            fieldName="workpadType"
            type="number"
            placeholder="Enter type"
            isEditable={isEditable}
          />
        );
      }
    },
    {
      accessorKey: 'workpadTitle',
      id: 'workpadTitle',
      header: t('workpad.workpadTitle') ?? 'Workpad Title',
      cell: (cellContext) => {
        const rowId = cellContext.row.id;
        const isNewRow = cellContext.row.original.newRowId;
        const isEditable = getEditMode ? (isNewRow ? getEditMode(undefined, isNewRow) : getEditMode(rowId)) : false;

        return (
          <EditableCell
            cellContext={cellContext}
            fieldName="workpadTitle"
            placeholder="Enter title"
            isEditable={isEditable}
          />
        );
      }
    },
    {
      accessorKey: 'closeDate',
      id: 'closeDate',
      header: t('workpad.postDate') ?? 'Post Date',
      cell: ({ row }) => row.original.closeDate || '-'
    },
    {
      accessorKey: 'status',
      id: 'status',
      header: t('workpad.workpadStatus') ?? 'Status',
      cell: ({ row }) => row.original.status || 'New'
    },
    {
      accessorKey: 'changedBy',
      id: 'changedBy',
      header: t('workpad.changedBy') ?? 'Changed By',
      cell: ({ row }) => {
        if (!row.original.changedBy) return '-';
        return (
          <Avatar color="tertiary" size="md">
            {row.original.changedBy}
          </Avatar>
        );
      }
    },
    {
      accessorKey: 'actions',
      id: 'actions',
      header: '',
      cell: (cellContext) => {
        const rowId = cellContext.row.id;
        const isNewRow = cellContext.row.original.newRowId;
        const isEditable = getEditMode ? (isNewRow ? getEditMode(undefined, isNewRow) : getEditMode(rowId)) : false;

        return (
          <ActionButtons
            cellContext={cellContext}
            onDuplicate={onDuplicate}
            onSaveNew={onSaveNew}
            onCancelNew={onCancelNew}
            onSaveExisting={onSaveExisting}
            isEditable={isEditable}
            setEditMode={setEditMode}
            clearEditMode={clearEditMode}
          />
        );
      }
    }
  ];
};

// Main Workpad Editable Table Component
interface WorkpadEditableTableProps {
  onRowSelection?: (row: Row<DynamicType>) => void;
  onDuplicate?: (workpadNo: number) => void;
  onCreateWorkpad?: () => void;
  onSave?: () => void;
  onPostItems?: () => void;
}

// Inner component that uses the SingleRowEdit context
const WorkpadEditableTableInner: React.FC<WorkpadEditableTableProps> = ({
  onRowSelection,
  onDuplicate,
  onCreateWorkpad,
  onSave,
  onPostItems
}) => {
  const { t } = useTranslation('fsw');
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});
  const [isCreatingNew, setIsCreatingNew] = useState(false);
  const [newWorkpadData, setNewWorkpadData] = useState<Partial<Workpads> | null>(null);
  const [editingRowId, setEditingRowId] = useState<string | null>(null);
  const [editingNewRowId, setEditingNewRowId] = useState<string | null>(null);

  // API mutations
  const createWorkpadMutation = useCreateWorkpad();
  const updateWorkpadMutation = useUpdateWorkpad();

  // Local edit mode functions that don't use context - memoized for stability
  const setEditMode = useCallback((rowId?: string, newRowId?: string) => {
    if (newRowId) {
      setEditingNewRowId(newRowId);
      setEditingRowId(null);
    } else {
      setEditingRowId(rowId || null);
      setEditingNewRowId(null);
    }
  }, []);

  const clearEditMode = useCallback(() => {
    setEditingRowId(null);
    setEditingNewRowId(null);
  }, []);

  const getEditMode = useCallback((rowId?: string, newRowId?: string) => {
    if (newRowId) {
      return editingNewRowId === newRowId;
    } else if (rowId) {
      return editingRowId === rowId;
    }
    return false;
  }, [editingRowId, editingNewRowId]);

  // Handle saving new workpad - memoized for stability
  const handleSaveNewWorkpad = useCallback((data: Partial<Workpads>) => {
    console.log('💾 Saving new workpad:', data);

    if (!data.workpadTitle || !data.workpadType) {
      console.error('❌ Missing required fields: workpadTitle and workpadType');
      return;
    }

    createWorkpadMutation.mutate(
      {
        workpadTitle: data.workpadTitle,
        workpadType: data.workpadType
      },
      {
        onSuccess: () => {
          console.log('✅ Workpad created successfully');
          setIsCreatingNew(false);
          setNewWorkpadData(null);
          clearEditMode();
        },
        onError: (error) => {
          console.error('❌ Failed to create workpad:', error);
        }
      }
    );
  }, [createWorkpadMutation, clearEditMode]);

  // Handle canceling new workpad - memoized for stability
  const handleCancelNewWorkpad = useCallback(() => {
    console.log('❌ Canceling new workpad creation');
    setIsCreatingNew(false);
    setNewWorkpadData(null);
    clearEditMode();
  }, [clearEditMode]);

  // Handle saving existing workpad - memoized for stability
  const handleSaveExistingWorkpad = useCallback((data: Partial<Workpads>) => {
    console.log('💾 Updating existing workpad:', data);

    if (!data.workpadNo || !data.workpadTitle || !data.workpadType) {
      console.error('❌ Missing required fields: workpadNo, workpadTitle, and workpadType');
      return;
    }

    updateWorkpadMutation.mutate(
      {
        workpadNo: data.workpadNo,
        workpadTitle: data.workpadTitle,
        workpadType: data.workpadType
      },
      {
        onSuccess: () => {
          console.log('✅ Workpad updated successfully');
          clearEditMode();
        },
        onError: (error) => {
          console.error('❌ Failed to update workpad:', error);
        }
      }
    );
  }, [updateWorkpadMutation, clearEditMode]);

  // Define columns after handlers are available
  const columns = useMemo(() =>
    useWorkpadEditableColumns(onDuplicate, handleSaveNewWorkpad, handleCancelNewWorkpad, handleSaveExistingWorkpad, getEditMode, setEditMode, clearEditMode),
    [onDuplicate, handleSaveNewWorkpad, handleCancelNewWorkpad, handleSaveExistingWorkpad, getEditMode, setEditMode, clearEditMode]
  );

  // Fetch workpad data using the existing service
  const { data, isLoading, isError } = useGetWorkpads({
    page: 1,
    size: 1000 // Fetch all records for now
  });

  // Double-click detection for row editing
  const useDoubleClick = () => {
    let clickCount = 0;
    let clickTimer: NodeJS.Timeout | null = null;

    return (row: Row<Workpads>) => {
      clickCount++;

      if (clickTimer) clearTimeout(clickTimer);

      clickTimer = setTimeout(() => {
        if (clickCount === 1) {
          // Single click - select row
          const newSelection = { [row.id]: !rowSelection[row.id] };
          setRowSelection(newSelection);
          onRowSelection?.(row as Row<DynamicType>);
        } else if (clickCount === 2) {
          // Double click - enter edit mode
          const isNewRow = row.original.newRowId;
          if (!isNewRow) {
            setEditMode(row.id);
            console.log('🎯 Double-clicked to edit workpad:', row.original.workpadTitle);
          }
        }
        clickCount = 0;
      }, 300);
    };
  };

  const handleRowClick = useDoubleClick();

  // Handle creating new workpad
  const handleCreateNewWorkpad = () => {
    if (isCreatingNew) return; // Prevent multiple new rows

    const newWorkpad: Partial<Workpads> = {
      workpadNo: 0, // Temporary ID for new row
      workpadType: 1,
      workpadTitle: '',
      closeDate: '',
      checkSumAmt: 0,
      status: 'New',
      changedBy: '',
      newRowId: 'new-workpad' // Special identifier for new rows
    };

    setNewWorkpadData(newWorkpad);
    setIsCreatingNew(true);

    // Enter edit mode for the new row
    setTimeout(() => {
      setEditMode(undefined, 'new-workpad');
    }, 100);

    onCreateWorkpad?.();
  };

  const tableData = useMemo(() => {
    const existingData = data?.content || [];
    if (isCreatingNew && newWorkpadData) {
      return [newWorkpadData as Workpads, ...existingData];
    }
    return existingData;
  }, [data?.content, isCreatingNew, newWorkpadData]);

  // Toolbar buttons
  const toolbarButtons = (
    <Box display="flex" gap="10px">
      <Button
        spacing="tight"
        color="secondary"
        onClick={handleCreateNewWorkpad}
        disabled={isCreatingNew}
      >
        {t('workpad.createWorkpad') || 'Create Workpad'}
      </Button>
      <Button
        spacing="tight"
        color="secondary"
        startIcon={<SaveIcon />}
        onClick={onSave}
      >
        {t('workpad.save') || 'Save'}
      </Button>
      <Button
        spacing="tight"
        color="primary"
        onClick={onPostItems}
      >
        {t('workpad.postItems') || 'Post Items'}
      </Button>
    </Box>
  );

  return (
    <DataTable<Workpads, unknown>
      columns={columns}
      data={tableData}
      tableId="fsw-workpad-editable-table"
      showPagination={false}
      showToolbar={true}
      showColumnFilters={true}
      enableRowSelection={true}
      rowSelection={rowSelection}
      setRowSelection={setRowSelection}
      onRowClick={handleRowClick}
      rightToolbarSlot={toolbarButtons}
      tableStatusIndicators={{
        isLoading,
        isError,
        errorText: t('workpad.errorText') || 'Error loading workpads',
        noRecordsText: t('workpad.noRecordsText') || 'No workpads found'
      }}
    />
  );
};

// Main component that provides the context
const WorkpadEditableTable: React.FC<WorkpadEditableTableProps> = (props) => {
  const compareRowIds = (id1: string, id2: string) => id1 === id2;

  return (
    <SingleRowEditProvider compareRowIds={compareRowIds}>
      <WorkpadEditableTableInner {...props} />
    </SingleRowEditProvider>
  );
};

export default WorkpadEditableTable;
