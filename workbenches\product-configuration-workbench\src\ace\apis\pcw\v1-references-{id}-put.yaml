path: /v1/references/{id}
method: put
flow: pcwUpdateReferences.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
definition:
  operationId: pcwUpdateReferences
  tags:
    - pcw
  summary: ""
  description: ""
  parameters:
    - in: path
      name: id
      required: true
      description: Unique reference ID
      schema:
        type: number
  requestBody:
    content:
      application/json: {}
  responses: {}
