path: /v1/ui-configuration-fields/{uiConfigurationObjectId}
method: delete
flow: pcwDeleteUiConfigurationFields.yaml
secured: false
errorHandlers:
  - PCW_UNAUTHORIZED.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_BAD_REQUEST.yaml
definition:
  operationId: pcwDeleteUiConfigurationFields
  tags:
    - pcw
  summary: Delete UI configuration fields
  description: ""
  parameters:
    - in: path
      name: uiConfigurationObjectId
      required: true
      description: UI Config Object UIX_SEQ_NO
      schema:
        type: number
  responses: {}
