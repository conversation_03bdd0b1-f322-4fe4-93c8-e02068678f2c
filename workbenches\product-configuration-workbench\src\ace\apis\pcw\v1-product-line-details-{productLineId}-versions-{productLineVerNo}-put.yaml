path: /v1/product-line-details/{productLineId}/versions/{productLineVerNo}
method: put
flow: pcwUpdateProductLineDetails.yaml
secured: false
errorHandlers:
  - PCW_UNAUTHORIZED.yaml
  - PCW_BAD_REQUEST.yaml
  - PCW_NOT_FOUND.yaml
definition:
  operationId: pcwUpdateProductLineDetails
  tags:
    - pcw
  summary: ""
  description: ""
  parameters:
    - in: path
      name: productLineId
      required: true
      description: ""
      schema:
        type: string
    - in: path
      name: productLineVerNo
      required: true
      description: ""
      schema:
        type: string
  requestBody:
    description: ""
    content:
      application/json:
        schema:
          $ref: pcwUpdateProductLineDetailsRequest.yaml
  responses: {}
