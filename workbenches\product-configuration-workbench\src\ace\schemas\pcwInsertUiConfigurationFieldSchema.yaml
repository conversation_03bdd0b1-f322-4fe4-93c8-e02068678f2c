description: Model documentation for ui config object payload
required:
  - productLineId
  - sortNo
  - uiConfigurationBlockName
  - uiConfigurationBlockVersion
  - uiConfigurationName
type: object
properties:
  productLineId:
    maxLength: 5
    minLength: 1
    type: string
    description: Identification of product line
  uiConfigurationName:
    type: string
    description: Name of Product Line UI Configuration
  uiConfigurationBlockName:
    type: string
    description: Name of Product Line UI Configuration Block
  uiConfigurationBlockVersion:
    type: number
    description: Version of Product Line UI Configuration Block
  columnName:
    type: string
    description: Colum name of the UI configuration column.
  fieldType:
    maxLength: 10
    minLength: 0
    type: string
    description: "The type of UI field to be rendered: AUTO, BUTTON, CALCULATED, LABEL, NOLABEL or PLCHOLDER"
  sortNo:
    type: integer
    description: Ranking of the order of how items should appear.
    format: int64
  length:
    maxLength: 10
    minLength: 0
    type: string
    description: "Length of the field. Valid values are: NORMAL, SMALLER or SMALLEST. Note: This attribute is only allowed on Object and Risk fields."
  rows:
    type: integer
    description: "To get a multi-line text field, the this property must be set.If the value is empty, 0, 1, then a single-line field is generated otherwise the field will be rendered as multi-line, where the value defines the number of Rows. Note: This attribute is only allowed on Object fields."
    format: int64
  mandatory:
    type: boolean
    description: Defines if a value for the field will be required on the object page.
  readOnly:
    type: boolean
    description: Indicates if UI column must open in read-only mode.
  displayCondition:
    type: string
    description: This property can be used to hide and show the field, depending on
      the value of other field in the Object Handling page. Syntax for the
      condition needs to follow the format <field> <operator> <value>
  function:
    type: string
    description: This property is only enabled for field_types Button and
      Calculated. Function can be chosen among the available functions already
      existing in the database UF_CONFIG_FUNC package.
  validationRule:
    maxLength: 30
    minLength: 0
    type: string
    description: "Reference to a entry in the field_validation_rule table. The rule code points to a regular expression validation. Note: This attribute is only allowed on Object and Subobject fields."
  defaultValueType:
    maxLength: 10
    minLength: 0
    type: string
    description: "Defines how to retrieve a default value for the actual field. Definition of the actual value to default has to be defined in the Default attribute: Fixed, Name Detail, Customer Detail Note: This attribute is only allowed on Object and Subobject fields."
  defaultValue:
    type: string
    description: "The default value to assign to the field. Note: This attribute is only allowed on Object and Subobject fields."
  lovType:
    maxLength: 30
    minLength: 0
    type: string
    description: "Task-Flow (a task-flow to be shown), Tariff Code, Reference table (XLA_REFERENCE / XLA_REFERENCE_BIG).  Note: This attribute is only allowed on Object and Subobject fields."
  lovName:
    maxLength: 30
    minLength: 0
    type: string
    description: "The name of the LOV component. The valid values depend upon the chosen LOV type. Note: This attribute is only allowed on Object and Subobject fields."
  filteringFunction:
    maxLength: 30
    minLength: 0
    type: string
    description: "Name of a procedure which is placed in UF_CONFIG_FUNC package. The procedure must return the additional logic to be used when rendering the LOV for the current field. Note: This attribute is only allowed on Object fields."
  dependentColumns:
    type: string
    description: "The field holds a flex field or Column name aliases separated by comma. If any of these fields are changed, the LOV for the current field will be re-queried. Note: This attribute is only allowed on Object fields."
  numberFormatType:
    maxLength: 3
    minLength: 3
    type: string
    description: "The number format type. AMT or NUM Note: This attribute is only allowed on Risk fields."
  columnLabel:
    type: string
    description: UI configuration column label.
