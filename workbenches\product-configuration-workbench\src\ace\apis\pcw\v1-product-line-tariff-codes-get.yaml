path: /v1/product-line-tariff-codes
method: get
flow: pcwGetProductLineTariffCodes.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
definition:
  operationId: pcwGetProductLineTariffCodes
  tags:
    - pcw
  summary: ""
  description: ""
  parameters:
    - in: query
      name: productLineId
      required: false
      description: Product line identification.
      schema:
        type: string
    - in: query
      name: productLineVerNo
      required: false
      description: Product line version.
      schema:
        type: number
    - in: query
      name: tableName
      required: false
      description: Table name. Partial text search.
      schema:
        type: string
    - in: query
      name: tableDescription
      required: false
      description: Table Description. Partial text search.
      schema:
        type: string
    - in: query
      name: page
      required: false
      description: Result page you want to retrieve (1..N)
      schema:
        type: integer
    - in: query
      name: size
      required: false
      description: Number of records per page
      schema:
        type: integer
  responses: {}
