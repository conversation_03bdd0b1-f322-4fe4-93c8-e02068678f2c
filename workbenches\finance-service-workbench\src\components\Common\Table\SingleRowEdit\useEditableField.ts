import { DynamicType } from '@sapiens/workbench-feature-library';
import { CellContext } from '@tanstack/react-table';

import { useSingleRowEdit } from './SingleRowEditProvider';

/**
 * Hook to determine if a field should be editable based on the current edit mode.
 * 
 * @param cellContext - The cell context from the table
 * @param enableEditing - Whether editing is enabled for this field
 * @returns boolean indicating if the field should be editable
 */
export const useEditableField = (cellContext: CellContext<DynamicType, unknown>, enableEditing: boolean = true): boolean => {
  const { getEditMode } = useSingleRowEdit();
  
  if (!enableEditing) {
    return false;
  }

  const rowId = cellContext.row.id;
  const originalRowData = cellContext.row.original;
  const isNewRow = originalRowData.newRowId;

  // Check if this row is in edit mode
  if (isNewRow) {
    return getEditMode(undefined, isNewRow);
  } else {
    return getEditMode(rowId);
  }
};
