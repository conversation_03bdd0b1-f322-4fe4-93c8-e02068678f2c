/// <reference types="cypress" />

import { loginUsingKeycloak, logoutUsingKeycloak } from '../../../cypress-utilities/cypress/index';
import ProductLineLeftSideModal from '../e2e/pageObjects/ProductLineLeftSideModal';
import ProductLineUiConfigurationPage from '../e2e/pageObjects/ProductLineUiConfigurationPage';
import ProductLines from '../e2e/pageObjects/ProductLines';

import 'cypress-keycloak';

const productLines = new ProductLines();

Cypress.Commands.add('loginWithKeycloak', () => {
  loginUsingKeycloak(
    Cypress.env('keycloak_root'),
    Cypress.env('keycloak_realm'),
    Cypress.env('keycloak_username'),
    Cypress.env('keycloak_password'),
    Cypress.env('keycloak_client_id'),
    Cypress.env('keycloak_redirect_uri')
  );
});

Cypress.Commands.add('logoutWithKeycloak', () => {
  logoutUsingKeycloak(Cypress.env('keycloak_root'), Cypress.env('keycloak_realm'), Cypress.env('keycloak_redirect_uri'));
});

Cypress.Commands.add('searchProductLineByID', (inputProductLineID) => {
  // Assuming productLines.getIdSearch() is the command to get the search input field
  cy.get('[data-testid="product-lines-id-filter-input"] > :first-child').clear().type(inputProductLineID).type('{enter}');
});

Cypress.Commands.add('createNewProductLine', (options) => {
  const {
    inputProductLineName,
    inputProductLineID,
    inputMainProductLineID,
    inputMainProductLineVersion,
    inputDefBackType,
    inputTempInsurance,
    inputYearsQuotation,
    inputYearsPolicy,
    inputComProductGroup,
    inputConditionOne,
    inputConditionTwo,
    inputConditionThree,
    inputVersionNotes
  } = options;

  // Add your reusable code here
  cy.get('button[type="button"]').contains('New').click();
  cy.get('[name="productLineName"]').type(inputProductLineName);
  cy.get('[name="productLineId"]').type(inputProductLineID);
  cy.get('[id="main-product-line-id-dropdown-searchable"]').type(inputMainProductLineID).type('{downarrow}').type('{enter}');
  cy.get('[id="main-product-line-version-dropdown-searchable"]').type(inputMainProductLineVersion).type('{downarrow}').type('{enter}');
  //select  backdatingtype
  cy.get('#mui-component-select-defaultBackdatingType').click();
  cy.contains('span', inputDefBackType).click();
  //select  temporary insurance
  cy.get('#mui-component-select-temporaryInsurance').click();
  cy.contains('span', inputTempInsurance).click();
  cy.get('[name="yearsToKeepQ"]').type(inputYearsQuotation);
  cy.get('[name="yearsToKeepP"]').type(inputYearsPolicy);
  cy.get('#mui-component-select-comProductGroup').click();
  cy.contains('span', inputComProductGroup).click();
  cy.get('[name="conditionSet1"]').type(inputConditionOne);
  cy.get('[name="conditionSet2"]').type(inputConditionTwo);
  cy.get('[name="conditionSet3"]').type(inputConditionThree);
  cy.get('[name="versionNotes"]').type(inputVersionNotes);
  cy.get('[name="renew"]').check();
  cy.get('[name="informationOnly"]').check();
  cy.get('[name="flatPremium"]').check();

  //Save product line
  cy.get('button[type="submit"]').contains('Save').click();
});

Cypress.Commands.add('verifyProductLineModal', (productLineModalData) => {
  const {
    lineInfo,
    prodLineStatus,
    productLineName,
    productLineID,
    mainProductLineId,
    mainProductLineVersion,
    defaultBackdatingType,
    temporaryInsurance,
    yearsQuotation,
    yearsPolicy,
    commissionProductGroup,
    conditionOne,
    conditionTwo,
    conditionThree,
    versionNotes
  } = productLineModalData;

  // Add your reusable code here
  cy.get('[data-testid="product-line-details-modal-form-title"]').should('include.text', lineInfo);
  cy.get('[data-testid="product-line-details-modal-form-status-badge"]').should('include.text', prodLineStatus);
  cy.get('[name="productLineName"]').should('have.value', productLineName);
  cy.get('[name="productLineId"]').should('have.value', productLineID);
  cy.get('[id="main-product-line-id-dropdown-searchable"]').should('have.value', mainProductLineId);
  cy.get('[id="main-product-line-version-dropdown-searchable"]').should('have.value', mainProductLineVersion);
  cy.get('[data-testid="default-backdating-type-dropdown"]').should('contain', defaultBackdatingType);
  cy.get('[id="mui-component-select-temporaryInsurance"]').should('contain', temporaryInsurance);
  cy.get('[name="yearsToKeepQ"]').should('have.value', yearsQuotation);
  cy.get('[name="yearsToKeepP"]').should('have.value', yearsPolicy);
  cy.get('[data-testid="commission-product-group-input"]').should('contain', commissionProductGroup);
  cy.get('[name="conditionSet1"]').should('have.value', conditionOne);
  cy.get('[name="conditionSet2"]').should('have.value', conditionTwo);
  cy.get('[name="conditionSet3"]').should('have.value', conditionThree);
  cy.get('[name="versionNotes"]').should('have.value', versionNotes);
  cy.get('[name="renew"]').should('be.checked');
  cy.get('[name="informationOnly"]').should('be.checked');
  cy.get('[name="flatPremium"]').should('be.checked');
});

Cypress.Commands.add('verifyProductLineModalUpdate', (productLineModalData) => {
  const {
    lineInfo,
    prodLineStatus,
    productLineName,
    productLineID,
    mainProductLineId,
    mainProductLineVersion,
    defaultBackdatingType,
    temporaryInsurance,
    yearsQuotation,
    yearsPolicy,
    commissionProductGroup,
    conditionOne,
    conditionTwo,
    conditionThree,
    versionNotes
  } = productLineModalData;

  // Add your reusable code here
  cy.get('[data-testid="product-line-details-modal-form-title"]').should('include.text', lineInfo);
  cy.get('[data-testid="product-line-details-modal-form-status-badge"]').should('include.text', prodLineStatus);
  cy.get('[name="productLineName"]').should('have.value', productLineName);
  cy.get('[name="productLineId"]').should('have.value', productLineID);
  cy.get('[id="main-product-line-id-dropdown-searchable"]').should('have.value', mainProductLineId);
  cy.get('[id="main-product-line-version-dropdown-searchable"]').should('have.value', mainProductLineVersion);
  cy.get('[data-testid="default-backdating-type-dropdown"]').should('contain', defaultBackdatingType);
  cy.get('[id="mui-component-select-temporaryInsurance"]').should('contain', temporaryInsurance);
  cy.get('[name="yearsToKeepQ"]').should('have.value', yearsQuotation);
  cy.get('[name="yearsToKeepP"]').should('have.value', yearsPolicy);
  cy.get('[data-testid="commission-product-group-input"]').should('contain', commissionProductGroup);
  cy.get('[name="conditionSet1"]').should('have.value', conditionOne);
  cy.get('[name="conditionSet2"]').should('have.value', conditionTwo);
  cy.get('[name="conditionSet3"]').should('have.value', conditionThree);
  cy.get('[name="versionNotes"]').should('have.value', versionNotes);
  cy.get('[name="renew"]').should('be.checked');
  cy.get('[name="informationOnly"]').should('be.checked');
  cy.get('[name="flatPremium"]').should('be.checked');
});

Cypress.Commands.add('checkProductLineMainInfo', (productLineMainData, inputProductLineID) => {
  const { productLineName, productLineVersion, productLineStatus, mainProductLineId, mainProductLineVersion } = productLineMainData;
  cy.searchProductLineByID(inputProductLineID);

  //cy.get('.sc-fUnMCh.MuiTypography-root.MuiTypography-default.jDHBvz').eq(1).should('include.text', productLineName);
  cy.get('[data-testid="product-lines-table-row"] > :nth-child(2)').should('include.text', productLineName);
  cy.get('[data-testid="product-lines-table-row"] > :nth-child(3)').should('include.text', productLineVersion);
  cy.get('[data-testid="product-lines-table-row"] > :nth-child(4)').should('include.text', productLineStatus);
  cy.get('[data-testid="product-lines-table-row"] > :nth-child(5)').should('include.text', mainProductLineId);
  cy.get('[data-testid="product-lines-table-row"] > :nth-child(6)').should('include.text', mainProductLineVersion);
});

Cypress.Commands.add('clickEditForSpecificProductLine', (inputProductLineID) => {
  cy.get('[data-testid="product-lines-id-filter-input"] > :first-child').clear().type(inputProductLineID).type('{enter}');
  // cy.get('.sc-kqGoIF.jwtrEq.MuiTableCell-root.MuiTableCell-body.MuiTableCell-sizeMedium.sc-gEkIjz.hagjF')
  //  .eq(7)
  cy.get('[data-testid="product-lines-table-row"] > :nth-child(4)').should('include.text', 'Dev').trigger('mouseover').click();
  cy.get('[data-testid="product-lines-table-table-cell-edit-icon"]').click();
});

Cypress.Commands.add('clickDeleteForSpecificProductLine', (inputProductLineID) => {
  cy.searchProductLineByID(inputProductLineID);
  cy.get('[data-testid="product-lines-table-row"] > :nth-child(4)').should('include.text', 'Dev').trigger('mouseover').click();
  cy.get('[data-testid="product-lines-table-table-cell-delete-icon"]').click();
  cy.contains('button', 'Delete').click();
  cy.get('.MuiTypography-root.MuiTypography-default').should('include.text', 'No product lines');
});

Cypress.Commands.add('updateAndSaveProductLine', (updatedProductLineModalData) => {
  const {
    updatedProductLineName,
    updatedMainProductLineVersion,
    updatedDefBackType,
    updatedTempInsurance,
    updatedYearsQuotation,
    updatedYearsPolicy,
    updatedComProductGroup,
    updatedConditionOne,
    updatedConditionTwo,
    updatedConditionThree,
    updatedVersionNotes
  } = updatedProductLineModalData;

  // Add your reusable code here
  cy.get('[name="productLineName"]').clear().type(updatedProductLineName);
  //cy.get(':nth-child(3) > :nth-child(4) > .sc-jMakVo > .sc-fUnMCh > .sc-cmaqmh > .sc-dAbbOL')
  cy.get('#main-product-line-version-dropdown-searchable')
    .type(updatedMainProductLineVersion)
    .type('{downarrow}')
    .type('{enter}');
  cy.get('#mui-component-select-defaultBackdatingType').click();
  cy.contains('span', updatedDefBackType).click();
  //select  temporary insurance
  cy.get('#mui-component-select-temporaryInsurance').click();
  cy.contains('span', updatedTempInsurance).click();
  cy.get('[name="yearsToKeepQ"]').clear().type(updatedYearsQuotation);
  cy.get('[name="yearsToKeepP"]').clear().type(updatedYearsPolicy);
  cy.get('#mui-component-select-comProductGroup').click();
  cy.contains('span', updatedComProductGroup).click();
  cy.get('[name="conditionSet1"]').clear().type(updatedConditionOne);
  cy.get('[name="conditionSet2"]').clear().type(updatedConditionTwo);
  cy.get('[name="conditionSet3"]').clear().type(updatedConditionThree);
  cy.get('[name="versionNotes"]').clear().type(updatedVersionNotes);
  cy.get('[name="renew"]').uncheck();
  cy.get('[name="informationOnly"]').uncheck();
  cy.get('[name="flatPremium"]').uncheck();

  //Save updated product line
  cy.get('button[type="submit"]').contains('Save').click();
  //cy.get('.footer > .MuiButton-text').click()
 // cy.get('button[type="button"]').contains('Cancel').click();
});

Cypress.Commands.add('verifyPartOfProductLineModal', (existingProductLineModalData) => {
  const {
    lineInfo,
    prodLineStatus,
    productLineName,
    productLineID,
    mainProductLineId,
    yearsQuotation,
    yearsPolicy,
    conditionOne,
    conditionTwo,
    conditionThree,
    versionNotes
  } = existingProductLineModalData;

  // Add your reusable code here
  cy.get('[data-testid="product-line-details-modal-form-title"').should('include.text', lineInfo);
  cy.get('[data-testid="product-line-details-modal-form-status-badge"').should('include.text', prodLineStatus);
  //cy.get('[name="productLineName"]').should('have.value', productLineName);
  cy.get('[data-testid="product-line-name-input"] input').should('have.value', productLineName);;
  cy.get('[name="productLineId"]').should('have.value', productLineID);
  cy.get('[name="versionNotes"]').should('have.value', versionNotes);
  cy.get('[name="renew"]').should('be.checked');
});

Cypress.Commands.add('updateAndSavePartOfProductLine', (updatedProductLinePartModalData) => {
  const { updatedProductLineName, updatedVersionNotes } = updatedProductLinePartModalData;

  // Add your reusable code here
  cy.get('[name="productLineName"]').clear().type(updatedProductLineName);
  cy.get('[name="versionNotes"]').clear().type(updatedVersionNotes);
  //Save updated product line
  cy.get('button[type="submit"]').contains('Save').click();
  //cy.get('button[type="button"]').contains('Cancel').click();
});

Cypress.Commands.add('checkMostRecentProductLineTab', (recentActivityProductLineInfo) => {
  const { productLineId, updatedProductLineName } = recentActivityProductLineInfo;
  //
  cy.get('.sc-eDPEul.dfrIhi.MuiTypography-root.MuiTypography-H300.sc-hTUWRQ.etkJwr')
    .should('include.text', 'PRODUCT CONFIGURATION WORKBENCH')
    .click();
    cy.get('.MuiTypography-H700').contains('Recent activity');
  // click on recent productLine tab
  cy.get('[data-testid="recent-product-lines-tab"').click();
  cy.reload();
  // check if the record is changed
  cy.get('.sc-iapWAC > :nth-child(1) > :nth-child(1)').contains(productLineId);
  cy.get('.sc-iapWAC > :nth-child(1) > :nth-child(2)').contains(updatedProductLineName);
});

Cypress.Commands.add('checkMostRecentProductsTab', (recentActivityProductInfo) => {
  const { productLineId, updatedProductLineName } = recentActivityProductInfo;
  cy.get('.sc-eDPEul.dfrIhi.MuiTypography-root.MuiTypography-H300.sc-hTUWRQ.etkJwr')
    .should('include.text', 'PRODUCT CONFIGURATION WORKBENCH')
    .click();
    cy.get('.MuiTypography-H700').contains('Recent activity');
  // click on recent product tab
  cy.get('[data-testid="recent-products-tab"').click();
  // check if the record is changed
  cy.get('.sc-iapWAC > :nth-child(1) > :nth-child(1)').contains(productLineId);
  cy.get('.sc-iapWAC > :nth-child(1) > :nth-child(2)').contains(updatedProductLineName);
});

Cypress.Commands.add('checkMostRecentTariffStructureTab', (recentActivityTariffStructuresInfo) => {
  const { tariffclass, type, description } = recentActivityTariffStructuresInfo;
  cy.get('.sc-eDPEul.dfrIhi.MuiTypography-root.MuiTypography-H300.sc-hTUWRQ.etkJwr')
    .should('include.text', 'PRODUCT CONFIGURATION WORKBENCH')
    .click();
  // cy.get('.sc-eDPEul.zWuDU.MuiTypography-root.MuiTypography-H700.sc-hTUWRQ.jTxSbT').contains('Recent activity');
  // click on recent tariff structure tab
  cy.get('[data-testid="recent-tariff-structure-tab"').click();
  // check if the record is changed
  cy.get('.sc-iapWAC > :nth-child(1) > :nth-child(1)').contains(tariffclass);
  cy.get('.sc-iapWAC > :nth-child(1) > :nth-child(2)').contains(type);
  cy.get('.sc-iapWAC > :nth-child(1) > :nth-child(3)').contains(description);
});

Cypress.Commands.add('checkProductLineMainData', (productLineMainData) => {
  const { productLineId, productLineName, productLineVersion, productLineStatus, mainProductLineId, mainProductLineVersion } =
    productLineMainData;
  //cy.searchProductLineByID(inputProductLineID);

  //cy.get('.sc-fUnMCh.MuiTypography-root.MuiTypography-default.jDHBvz').eq(1).should('include.text', productLineName);
  cy.get('[data-testid="product-lines-table-row"] > :nth-child(1)').should('include.text', productLineId);
  cy.get('[data-testid="product-lines-table-row"] > :nth-child(2)').should('include.text', productLineName);
  cy.get('[data-testid="product-lines-table-row"] > :nth-child(3)').should('include.text', productLineVersion);
  cy.get('[data-testid="product-lines-table-row"] > :nth-child(4)').should('include.text', productLineStatus);
  cy.get('[data-testid="product-lines-table-row"] > :nth-child(5)').should('include.text', mainProductLineId);
  cy.get('[data-testid="product-lines-table-row"] > :nth-child(6)').should('include.text', mainProductLineVersion);
});

Cypress.Commands.add('searchProductLineByName', (inputProductLineName) => {
  cy.get('[data-testid="product-lines-id-filter-input"] > :first-child').clear().type(inputProductLineName).type('{enter}');
});

Cypress.Commands.add('searchProductLineByVersion', (inputProductLineVersion) => {
  cy.get('[data-testid="product-lines-ver-no-filter-input"] > :first-child').clear().type(inputProductLineVersion).type('{enter}');
});

Cypress.Commands.add('searchProductLineByMainPlId', (inputProductLineMainPlId) => {
  cy.get('[data-testid="main-product-line-id-filter-input"] > :first-child').clear().type(inputProductLineMainPlId).type('{enter}');
});

Cypress.Commands.add('searchProductLineByMainPlVersion', (inputProductLineMainPlVersion) => {
  cy.get('[data-testid="main-product-line-ver-no-filter-input"] > :first-child')
    .clear()
    .type(inputProductLineMainPlVersion)
    .type('{enter}');
});

Cypress.Commands.add('searchProductLineByStatus', (inputProductLineStatus) => {
  cy.get('#status-select').click();
  cy.get(`[data-value="${inputProductLineStatus}"]`).click(); // Use inputProductLineStatus here
});

Cypress.Commands.add('checkNumberOfRows', (numberRows) => {
  cy.get('.MuiTableBody-root').as('tbody');
  // Check if the tbody element is found
  cy.get('@tbody').should('exist');

  // Count the number of rows inside the tbody element
  cy.get('@tbody').find('tr').should('have.length', numberRows);
});

Cypress.Commands.add('checkColumnStatusValues', (statusValue) => {
  // Find all the elements in column 4
  cy.get('@tbody')
    .find('tr') // Select all the table rows
    .each(($row) => {
      // Iterate over each row
      cy.wrap($row).find('td:nth-child(4)').should('contain.text', statusValue); // Check the text content of the 4th column
    });
});

// Custom command to check pagination page number
Cypress.Commands.add('checkOnlyPaginationNumberOneExist', (pageNumber) => {
  cy.get('button.MuiPaginationItem-page[aria-current="true"]').should('have.text', pageNumber);
});

Cypress.Commands.add('checkPaginationPageExistence', (pageNumber) => {
  // Construct the selector for the button corresponding to the given page number
  const pageButtonSelector = `.MuiPaginationItem-page[aria-label="Go to page ${pageNumber}"]`;
  // Check if the button corresponding to the given page number exists
  cy.get(pageButtonSelector).should('exist');
});

Cypress.Commands.add('checkPaginationPageNonExistence', (pageNumber) => {
  // Construct the selector for the button corresponding to the given page number
  const pageButtonSelector = `.MuiPaginationItem-page[aria-label="Go to page ${pageNumber}"]`;

  // Check if the button corresponding to the given page number exists
  cy.get(pageButtonSelector).should('not.exist');
});

Cypress.Commands.add('clickPagesAndCheckStatusValue', (statusValue, fromPage, tillPage) => {
  // Loop through pages from 'fromPage' to 'tillPage'
  for (let i = fromPage; i <= tillPage; i++) {
    // Click on the page number button
    cy.get(`.MuiPaginationItem-page[aria-label="Go to page ${i}"]`).click();

    // Check the status value on the current page
    cy.checkColumnStatusValues(statusValue);
  }
});

Cypress.Commands.add('navigateToUiConfigurationPage', (inputProductLineID, inputProductLineIdVersion) => {
  cy.get('[data-testid="navigate-to-product-lines"]').click();
  cy.searchProductLineByID(inputProductLineID);
  cy.get('[data-testid="product-lines-table-row"]')
    .contains('a[href="/pcw/product-lines/' + inputProductLineID + '/version/' + inputProductLineIdVersion + '"]', inputProductLineID)
    .click();
  cy.get('[data-testid="risk-and-objects-tab"]').should('have.attr', 'aria-selected', 'true');
  cy.get('[data-testid="ui-configuration-tab"]').click();
});

Cypress.Commands.add('verifyUiConfigurationPage', () => {
  const productLineUiConfigurationPage = new ProductLineUiConfigurationPage();

  productLineUiConfigurationPage.getUiConfigurationTabButton().should('have.attr', 'aria-selected', 'true');
  productLineUiConfigurationPage.getBreadcrumbs().contains('Product Line Details');
  productLineUiConfigurationPage.getSaveButton().scrollIntoView().should('be.visible');
  productLineUiConfigurationPage.getCancelButton().scrollIntoView().should('be.visible');
  productLineUiConfigurationPage.getUiConfigurationList().scrollIntoView().should('be.visible');
  productLineUiConfigurationPage.getUiConfigurationListVersionAndStatus().contains('span', '1 Dev');
  productLineUiConfigurationPage.getAddNewUiConfigButton().contains('button', 'New configuration');

  //assertions about the UI configuration filter
  cy.viewport(2000, 800);

  productLineUiConfigurationPage.getUiFilterNameInput().should('be.visible');
  productLineUiConfigurationPage.getUiDescriptionFilterInput().scrollIntoView().should('be.visible');
  productLineUiConfigurationPage.getUiConfigVersionFilterInput().scrollIntoView().should('be.visible');
  productLineUiConfigurationPage.getStatusSelect().scrollIntoView().should('be.visible');
  productLineUiConfigurationPage.getObjectType().scrollIntoView().should('be.visible');
});

Cypress.Commands.add('verifyLeftSidePanelModalStatus', (producLineLeftPanelStatusInfo) => {
  const {
    productLineStatus,
    riskStatus,
    objectLinkStatus,
    uiConfigurationStatus,
    clausesStatus,
    businessRulesStatus,
    tariffStructureStatus,
    globalTariffStructureName,
    globalTariffStructureStatus,
    tariffAlgorithmStatus,
    renewalAlgorithmStatus,
    indexationStatus,
    coverCheckStatus
  } = producLineLeftPanelStatusInfo;

  const productLineLeftSideModal = new ProductLineLeftSideModal();


  productLineLeftSideModal.getProductLineStatus().find('span').should('have.text', productLineStatus);

  productLineLeftSideModal.getRiskStatus().find('span').should('have.text', riskStatus);
  productLineLeftSideModal.getObjectStatus().find('span').should('have.text', objectLinkStatus);
  productLineLeftSideModal.getUiConfigurationStatus().find('span').should('have.text', uiConfigurationStatus);
  productLineLeftSideModal.getClausesStatus().find('span').should('have.text', clausesStatus);
  productLineLeftSideModal.getBusinessRulesStatus().should('have.text', businessRulesStatus);
  productLineLeftSideModal.getTariffStructureStatus().should('have.text', tariffStructureStatus);
  productLineLeftSideModal.getGlobalTariffStructureName().should('have.text', globalTariffStructureName);
  productLineLeftSideModal.getGlobalTariffStructureStatus().should('have.text', globalTariffStructureStatus);
  productLineLeftSideModal.getTariffAlgorithmStatus().should('have.text', tariffAlgorithmStatus);
  productLineLeftSideModal.getRenewalAlgorithmStatus().should('have.text', renewalAlgorithmStatus);
  productLineLeftSideModal.getIndexationStatus().should('have.text', indexationStatus);
  productLineLeftSideModal.getCoverCheckStatus().should('have.text', coverCheckStatus);
});

Cypress.Commands.add('verifySecondaryLeftSidePanelWithStatusProduction', () => {

  const productLineLeftSideModal = new ProductLineLeftSideModal();
  productLineLeftSideModal.getRiskStatus().should('have.class', 'MuiInputBase-readOnly')
    .should('have.text', '1 P');
  productLineLeftSideModal.getObjectStatus().should('have.class', 'MuiInputBase-readOnly')
    .should('have.text', '1 P');
  productLineLeftSideModal.getUiConfigurationStatus().should('have.class', 'MuiInputBase-readOnly')
    .should('have.text', '1 P');
  productLineLeftSideModal.getClausesStatus().should('have.class', 'MuiInputBase-readOnly')
    .should('have.text', '1 P');
  productLineLeftSideModal.getBusinessRulesStatus().should('have.class', 'MuiInputBase-readOnly')
    .should('have.text', '1 P');
  productLineLeftSideModal.getTariffStructureStatus().should('have.class', 'MuiInputBase-readOnly')
    .should('have.text', '1 P');
  productLineLeftSideModal.getGlobalTariffStructureName().should('have.class', 'MuiInputBase-readOnly');;
  productLineLeftSideModal.getGlobalTariffStructureStatus().should('have.class', 'MuiInputBase-readOnly');
  productLineLeftSideModal.getTariffAlgorithmStatus().should('have.class', 'MuiInputBase-readOnly')
    .should('have.text', '1 P');
  productLineLeftSideModal.getRenewalAlgorithmStatus().should('have.class', 'MuiInputBase-readOnly')
    .should('have.text', '1 P');
  productLineLeftSideModal.getIndexationStatus().should('have.class', 'MuiInputBase-readOnly');
  productLineLeftSideModal.getCoverCheckStatus().should('have.class', 'MuiInputBase-readOnly');
});

Cypress.Commands.add('checkEditButtonForSpecificProductLine', (inputProductLineID, shouldExist) => {
  const productLines = new ProductLines();
  productLines.getIdSearch().clear().type(inputProductLineID).type('{enter}');

  cy.get('[data-testid="product-lines-table-row"] > :nth-child(4)')
    .should('include.text', shouldExist ? 'Dev' : 'Prod') // Check if the correct row is selected based on shouldExist argument
    .trigger('mouseover')
    .click();

  if (shouldExist) {
    productLines.getEditButton().should('exist'); // Check if the edit button exists if shouldExist is true
  } else {
    productLines.getEditButton().should('not.exist'); // Check if the edit button does not exist if shouldExist is false
  }
});

Cypress.Commands.add('checkDeleteButtonForSpecificProductLine', (inputProductLineID, shouldExist) => {
  const productLines = new ProductLines();
  productLines.getIdSearch().clear().type(inputProductLineID).type('{enter}');

  cy.get('[data-testid="product-lines-table-row"] > :nth-child(4)')
    .should('include.text', shouldExist ? 'Dev' : 'Prod') // Check if the correct row is selected based on shouldExist argument
    .trigger('mouseover')
    .click();

  if (shouldExist) {
    productLines.getDeleteButton().should('exist'); // Check if the delete button exists if shouldExist is true
  } else {
    productLines.getDeleteButton().should('not.exist'); // Check if the delete button does not exist if shouldExist is false
  }
});

Cypress.Commands.add('checkViewButtonForSpecificProductLine', (inputProductLineID, shouldExist) => {
  const productLines = new ProductLines();
  productLines.getIdSearch().clear().type(inputProductLineID).type('{enter}');

  cy.get('[data-testid="product-lines-table-row"] > :nth-child(4)')
    .should('include.text', shouldExist ? 'Prod' : 'Dev') // Check if the correct row is selected based on shouldExist argument
    .trigger('mouseover')
    .click();

  if (shouldExist) {
    productLines.getViewButton().should('exist'); // Check if the view button exists if shouldExist is true
  } else {
    productLines.getViewButton().should('not.exist'); // Check if the view button does not exist if shouldExist is false
  }
});

Cypress.Commands.add('updateShortDescriptionBlock', (columnsShortDescription, textsShortDescription) => {
  const productLines = new ProductLines();

  for (let i = 0; i < columnsShortDescription.length; i++) {
    const column = columnsShortDescription[i];
    const text = textsShortDescription[i];

    cy.get(`[id="table-${i + 1}-dropdown-searchable"]`)
      .click()
      .type('Policy line')
      .type('{downarrow}')
      .type('{enter}');
    cy.get(`[id="column-${i + 1}-dropdown-searchable"]`)
      .click()
      .type(column)
      .type('{downarrow}')
      .type('{enter}');
    cy.get(`[data-testid="short-desc-text-${i + 1}-input"]`)
      .click()
      .type(text);
  }
});

Cypress.Commands.add('verifyShortDescriptionBlock', (columnsShortDescription, textsShortDescription) => {
  for (let i = 0; i < columnsShortDescription.length; i++) {
    const column = columnsShortDescription[i];
    const text = textsShortDescription[i];

    cy.get(`[id="table-${i + 1}-dropdown-searchable"]`).should('have.value', 'Policy line'); // Verify table dropdown value

    cy.get(`[id="column-${i + 1}-dropdown-searchable"]`).should('have.value', column); // Verify column dropdown value

    cy.get(`[data-testid="short-desc-text-${i + 1}-input"] > :first-child`).should('have.value', text); // Verify short description text input value
  }
});

Cypress.Commands.add('clickViewForSpecificProductLine', (inputProductLineID) => {
  cy.get('[data-testid="product-lines-id-filter-input"] > :first-child').clear().type(inputProductLineID).type('{enter}');
  cy.get('[data-testid="product-lines-table-row"] > :nth-child(4)').should('include.text', 'Prod').trigger('mouseover').click();
  cy.get('[data-testid="product-lines-table-table-cell-view-icon"]').click();
});

Cypress.Commands.add('verifyAllProductLineModalFieldsInReadOnlyMode', () => {
  cy.get('[data-testid="product-line-name-input"] input').should('have.attr', 'readonly');
  cy.get('[data-testid="product-line-id-input"] input').should('have.attr', 'readonly');
  cy.get('[id="main-product-line-id-dropdown-searchable"]').should('have.attr', 'readonly');
  cy.get('#main-product-line-id-dropdown-searchable').should('have.attr', 'readonly');
  cy.get('[data-testid="default-backdating-type-dropdown"]').should('have.class', 'Mui-readOnly');
  cy.get('[data-testid="temporary-insurance-dropdown"]').should('have.class', 'Mui-readOnly');
  cy.get('[data-testid="years-to-keep-q-input"]').should('have.class', 'Mui-readOnly');
  cy.get('[data-testid="years-to-keep-p-input"]').should('have.class', 'Mui-readOnly');
  cy.get('[data-testid="commission-product-group-input"]').should('have.class', 'Mui-readOnly');
  cy.get('[data-testid="condition-set-1-input"]').should('have.class', 'Mui-readOnly');
  cy.get('[data-testid="condition-set-2-input"]').should('have.class', 'Mui-readOnly');
  cy.get('[data-testid="condition-set-3-input"]').should('have.class', 'Mui-readOnly');
  cy.get('[data-testid="version-notes-input"]').should('have.class', 'Mui-readOnly');
  cy.get('[data-testid="renew-checkbox"]').should('have.class', 'Mui-disabled');
  cy.get('[data-testid="information-only-checkbox"]').should('have.class', 'Mui-disabled');
  cy.get('[data-testid="flat-premium-checkbox"]').should('have.class', 'Mui-disabled');
  cy.get('#table-1-dropdown-searchable').should('have.class', 'Mui-readOnly');
  cy.get('#table-2-dropdown-searchable').should('have.class', 'Mui-readOnly');
  cy.get('#table-3-dropdown-searchable').should('have.class', 'Mui-readOnly');
  cy.get('#table-4-dropdown-searchable').should('have.class', 'Mui-readOnly');
  cy.get('#column-1-dropdown-searchable').should('have.class', 'Mui-readOnly');
  cy.get('#column-2-dropdown-searchable').should('have.class', 'Mui-readOnly');
  cy.get('#column-3-dropdown-searchable').should('have.class', 'Mui-readOnly');
  cy.get('#column-4-dropdown-searchable').should('have.class', 'Mui-readOnly');
  cy.get('[data-testid="short-desc-text-1-input"]').should('have.class', 'Mui-readOnly');
  cy.get('[data-testid="short-desc-text-2-input"]').should('have.class', 'Mui-readOnly');
  cy.get('[data-testid="short-desc-text-3-input"]').should('have.class', 'Mui-readOnly');  
  cy.get('[data-testid="short-desc-text-4-input"]').should('have.class', 'Mui-readOnly');
});

Cypress.Commands.add('filterUIConfig', (filterValue) => {
  cy.get('[data-testid="ui-config-name-filter-input"] > :first-child').type(filterValue);
});

Cypress.Commands.add('selectColumnName', (columnName) => {
  cy.get('[id="column-name-dropdown-searchable"]').type(columnName).type('{downarrow}').type('{enter}');
});

Cypress.Commands.add('verifyColumnName', (expectedValue) => {
  cy.get('#column-name-dropdown-searchable').should('have.value', expectedValue);
});

Cypress.Commands.add('enterOrderValue', (value) => {
  cy.get('[data-testid="sort-no"] input').clear().type(value);
});

Cypress.Commands.add('verifyOrderValue', (value) => {
  cy.get('[data-testid="sort-no"] input').should('have.value', value);
});

Cypress.Commands.add('clickButtonWithText', (text) => {
  cy.contains('button', text).click();
});

Cypress.Commands.add('selectOptionFromDropdown', (optionText) => {
  cy.get('[data-testid="length"]').click();
  cy.get('ul[role="listbox"]').should('be.visible');
  cy.contains('li[role="option"]', optionText).click();
});

Cypress.Commands.add('verifyLengthField', (expectedText) => {
  cy.get('[data-testid="length"]').should('contain', expectedText);
});

Cypress.Commands.add('enterDisplayCondition', (text) => {
  cy.get('[data-testid="display-condition"]').clear().type(text);
});

Cypress.Commands.add('enterRowsValue', (value) => {
  cy.get('div[data-testid="rows"] input').clear().type(value);
});

Cypress.Commands.add('checkMandatoryCheckbox', () => {
  cy.get('[data-testid="mandatory"] > :nth-child(1)').check();
});

Cypress.Commands.add('checkReadOnlyCheckbox', () => {
  cy.get('[data-testid="read-only"] > :nth-child(1)').check();
});

Cypress.Commands.add('chooseDefaultValueType', (value) => {
  cy.get('[data-testid="default-value-type"]').click();
  cy.get('[role="option"]').contains(value).click();
});

Cypress.Commands.add('enterDefaultValue', (value) => {
  cy.get('[data-testid="default-value"] input').clear().type(value);
});

Cypress.Commands.add('chooseLOVType', (optionText) => {
  cy.get('[data-testid="lov-type"]').click(); 
  cy.contains('span', optionText).click();  
});

Cypress.Commands.add('chooseLOV', (optionText) => {
  cy.get('[id="lov-dropdown-searchable"]').type(optionText).type('{downarrow}').type('{enter}');
});

Cypress.Commands.add('saveUIConfig', () => {
  cy.contains('button', 'Save').click();
});

Cypress.Commands.add('verifySuccessMessage', () => {
  cy.get('.Toastify__toast--success')
    .should('be.visible')
    .within(() => {
      cy.get('.MuiTypography-root')
        .should('contain', 'Success Changes were saved.');
});
});

Cypress.Commands.add('verifyDeleteSuccessMessage', () => {
  //cy.get('.MuiTypography-medium ').should('be.visible').contains(' Data was deleted successfully.');
  cy.get('.MuiTypography-medium')
  .should('contain.text', ' Data was deleted successfully.')
  .should('be.visible');

});

Cypress.Commands.add('verifyFieldType', (expectedFieldType) => {
  cy.get('[data-testid="field-type"]').within(() => {
    cy.get('input').should('have.value', expectedFieldType);
  });
});

Cypress.Commands.add('verifyDisplayCondition', (expectedValue) => {
  cy.get('textarea[data-testid="display-condition"]').should('have.value', expectedValue);
});

Cypress.Commands.add('verifyRowsValue', (expectedValue) => {
  cy.get('div[data-testid="rows"] input').should('have.value', expectedValue);
});

Cypress.Commands.add('verifyCheckboxChecked', (dataTestId, checked) => {
  if (checked) {
    cy.get(`span[data-testid="${dataTestId}"]`).find('svg#checked-icon').should('exist');
  } else {
    cy.get(`span[data-testid="${dataTestId}"]`).find('svg#unchecked-icon').should('exist');
  }
});

Cypress.Commands.add('verifyDefaultValueType', (expectedValue) => {
  cy.get('[data-testid="default-value-type"] span').should('contain', expectedValue);
});

Cypress.Commands.add('verifyDefaultValue', (expectedValue) => {
  cy.get('[data-testid="default-value"] input').should('have.value', expectedValue);
});

Cypress.Commands.add('verifyLovType', (expectedValue) => {
  cy.get('[data-testid="lov-type"] input').should('have.value', expectedValue);
});

Cypress.Commands.add('verifyLovValue', (expectedValue) => {
  cy.get('#lov-dropdown-searchable').should('have.value', expectedValue);
});

Cypress.Commands.add('uncheckCheckbox', (dataTestId) => {
  cy.get(`[data-testid="${dataTestId}"] > :nth-child(1)`).uncheck();
});

Cypress.Commands.add('enterFilteringFunction', (text) => {
  cy.get('#filtering-function-dropdown-searchable').click().type(text).type('{downarrow}').type('{enter}');
});

Cypress.Commands.add('enterDependentColumn', (text) => {
  cy.get(`[data-testid="dependent-columns"] input`).type(text);
});

Cypress.Commands.add('enterValidationRule', (text) => {
  cy.get('#validation-rule-dropdown-searchable').click().type(text).type('{downarrow}').type('{enter}');
});

Cypress.Commands.add('verifyIFilteringfunction', (expectedValue) => {
  cy.get(`input[id="filtering-function-dropdown-searchable"]`).should('have.value', expectedValue);
});

Cypress.Commands.add('verifyDependetColumns', (expectedValue) => {
  cy.get(`div[data-testid="dependent-columns"] input`).should('have.value', expectedValue);
});

Cypress.Commands.add('verifyValidationRule', (expectedValue) => {
  cy.get(`input[id="validation-rule-dropdown-searchable"]`).should('have.value', expectedValue);
});

Cypress.Commands.add('enterSubobjectOrderValue', (value) => {
  cy.get('div[data-testid="UiConfigSubObjectFieldOrder"] input[type="text"]').clear().type(value);
});

Cypress.Commands.add('checkSubobjectMandatoryCheckbox', () => {
  cy.get('[data-testid="UiConfigSubObjectFieldMandatory"] > :nth-child(1)').check();
});

Cypress.Commands.add('checkSubobjectReadOnlyCheckbox', () => {
  cy.get('[data-testid="UiConfigSubObjectFieldReadOnly"] > :nth-child(1)').check();
});

Cypress.Commands.add('verifySubobjectCheckboxChecked', (dataTestId, checked) => {
  if (checked) {
    cy.get(`span[data-testid="${dataTestId}"]`).find('svg#checked-icon').should('exist');
  } else {
    cy.get(`span[data-testid="${dataTestId}"]`).find('svg#unchecked-icon').should('exist');
  }
});

Cypress.Commands.add('chooseSubobjectLovType', (optionText) => {
  cy.get('[data-testid="lov-type-type"]').click(); 
  cy.contains('span', optionText).click();  
});

Cypress.Commands.add('verifySubobjectLovType', (expectedValue) => {
  cy.get('[data-testid="lov-type-type"] input').should('have.value', expectedValue);
});

Cypress.Commands.add('chooseSubobjectLov', (optionText) => {
  cy.get('[id="lov-dropdown-searchable"]').type(optionText).type('{downarrow}').type('{enter}');
});

Cypress.Commands.add('verifySubobjectLovValue', (expectedValue) => {
  cy.get('#lov-dropdown-searchable').should('have.value', expectedValue);
});

Cypress.Commands.add('verifySubobjectOrderValue', (value) => {
  cy.get('div[data-testid="UiConfigSubObjectFieldOrder"] input[type="text"]').should('have.value', value);
});

Cypress.Commands.add('selectFunction', (columnName) => {
  cy.get('[id="function-dropdown-searchable"]').type(columnName).type('{downarrow}').type('{enter}');
});

Cypress.Commands.add('verifyFunction', (value) => {
  cy.get('input#function-dropdown-searchable').should('have.value', value);
});

  Cypress.Commands.add('fillFormData', () => {
    const formData = [
      { index: 0, displayCode: '1', description: 'Description_tia', helpText: 'help_tia', sortNo: '11' },
      { index: 1, displayCode: '2', description: 'Description_da', helpText: 'help_da', sortNo: '22' },
      { index: 2, displayCode: '3', description: 'Description_en', helpText: 'help_en', sortNo: '33' },
    ];
  
    // Fill data
    formData.forEach(data => {
      cy.get(`input[name="formData.${data.index}.displayCode"]`)
        .clear()
        .type(data.displayCode)
        .should('have.value', data.displayCode);
      cy.get(`input[name="formData.${data.index}.description"]`)
        .clear()
        .type(data.description)
        .should('have.value', data.description);
      cy.get(`input[name="formData.${data.index}.helpText"]`)
        .clear()
        .type(data.helpText)
        .should('have.value', data.helpText);
      cy.get(`input[name="formData.${data.index}.sortNo"]`)
      .clear()
        .type(data.sortNo)
        .should('have.value', data.sortNo);
    });
  
    // Save the form
    cy.get('button[type="submit"][form="TranslationModalForm"]').click();

  });
  
  Cypress.Commands.add('verifyFormData', () => {
    const formData = [
      { index: 0, displayCode: '1', description: 'Description_tia', helpText: 'help_tia', sortNo: '11' },
      { index: 1, displayCode: '2', description: 'Description_da', helpText: 'help_da', sortNo: '22' },
      { index: 2, displayCode: '3', description: 'Description_en', helpText: 'help_en', sortNo: '33' },
    ];
  
    // Verify data
    formData.forEach(data => {
      cy.get(`input[name="formData.${data.index}.displayCode"]`).should('have.value', data.displayCode);
      cy.get(`input[name="formData.${data.index}.description"]`).should('have.value', data.description);
      cy.get(`input[name="formData.${data.index}.helpText"]`).should('have.value', data.helpText);
      cy.get(`input[name="formData.${data.index}.sortNo"]`).should('have.value', data.sortNo);
  });
  cy.get('[data-testid="pending-cancel-button-id"]').click();
});

Cypress.Commands.add('fillRiskFormData', () => {
  const formData = [
    { index: 0, displayCode: '1', description: 'Description_tia', helpText: 'help_tia' },
    { index: 1, displayCode: '2', description: 'Description_da', helpText: 'help_da' },
    { index: 2, displayCode: '3', description: 'Description_en', helpText: 'help_en' },
  ];

  // Fill data
  formData.forEach(data => {
    cy.get(`input[name="formData.${data.index}.displayCode"]`)
      .clear()
      .type(data.displayCode)
      .should('have.value', data.displayCode);
    cy.get(`input[name="formData.${data.index}.description"]`)
      .clear()
      .type(data.description)
      .should('have.value', data.description);
    cy.get(`input[name="formData.${data.index}.helpText"]`)
      .clear()
      .type(data.helpText)
      .should('have.value', data.helpText);
  });

  // Save the form
  cy.get('button[type="submit"][form="TranslationModalForm"]').click();
});

Cypress.Commands.add('verifyRiskFormData', () => {
  const formData = [
    { index: 0, displayCode: '1', description: 'Description_tia', helpText: 'help_tia' },
    { index: 1, displayCode: '2', description: 'Description_da', helpText: 'help_da' },
    { index: 2, displayCode: '3', description: 'Description_en', helpText: 'help_en' },
  ];

  // Verify data
  formData.forEach(data => {
    cy.get(`input[name="formData.${data.index}.displayCode"]`).should('have.value', data.displayCode);
    cy.get(`input[name="formData.${data.index}.description"]`).should('have.value', data.description);
    cy.get(`input[name="formData.${data.index}.helpText"]`).should('have.value', data.helpText);
  });

  // Click the pending cancel button
  cy.get('[data-testid="pending-cancel-button-id"]').click();
});
