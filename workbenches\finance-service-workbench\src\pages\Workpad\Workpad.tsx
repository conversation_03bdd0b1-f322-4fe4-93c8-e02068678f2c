import SaveIcon from '@mui/icons-material/Save';
import { Accordion, Box, Button, Card, Menu } from '@sapiens/insuredpro-ui-component-library';
import { AlertBar, DataTable, PagePlaceholderIds, useTranslation } from '@sapiens/workbench-feature-library';
import { Row } from '@tanstack/react-table';
import { useEffect, useMemo, useState } from 'react';
import { useLocation, useParams } from 'react-router-dom';

import MenuPanel from '@/components/PageLayout/LeftSidePanel/MenuPanel';
import PageLayout from '@/components/PageLayout/PageLayout';
import PageLayoutGridWrapper from '@/components/PageLayout/PageLayoutGridWrapper';
import { ActivityLogPanel } from '@/components/PageLayout/RightSidePanel/ActivityLogPanel';
import BreadcrumbsPanel from '@/components/Pages/Overview/BreadcrumbsPanel';
import WorkpadItemDetailsForm from '@/components/Pages/Workpad/components/ItemDetails/WorkpadItemDetailsForm';
import { DynamicWidget } from '@/components/Widget/DynamicWidget';
import { Workpads } from '@/models/workpads.model';
import useGetParty from '@/pages/AccountItems/hooks/useGetParty';
import { useCreateWorkpad, useGetWorkpads } from '@/services/workpad/workpad.service';

import { AccountItemsTable } from '../../components/Pages/Workpad/components/AccountItems/AccountItemsTable';
import { useWorkpadColumns } from './hooks/useWorkpadColumns';

export const Workpad = () => {
  const { t } = useTranslation('fsw');
  const { mutate: createWorkpad } = useCreateWorkpad();
  const routeParams = useParams();
  const location = useLocation();
  const { partyId } = useGetParty({ ...routeParams });
  const columns = useWorkpadColumns({
    isCreatingNewWorkpad,
    onSaveNewWorkpad: handleSaveNewWorkpad,
    onCancelNewWorkpad: handleCancelNewWorkpad
  });
  const tableOptions = useMemo(
    () => ({
      sorting: [{ id: 'workpadNo', desc: false }],
      showToolbar: true,
      showColumnFilters: true,
      manualSorting: true,
      manualFiltering: true,
      enableRowSelection: true,
      noRecordsText: t('workpad.noRecordsText'),
      errorText: t('workpad.errorText')
    }),
    [t]
  );

  const [sorting, setSorting] = useState(tableOptions.sorting);
  const [columnFilters, setColumnFilters] = useState([]);
  const [rowSelection, setRowSelection] = useState<Record<number, boolean>>({});
  const [selectedWorkpadNo, setSelectedWorkpadNo] = useState<number | null>(null);
  const [isCreatingNewWorkpad, setIsCreatingNewWorkpad] = useState(false);
  const [newWorkpadData, setNewWorkpadData] = useState<Partial<Workpads> | null>(null);

  // Fetch all records by setting a large page size
  const { data, isLoading, isError, error, refetch } = useGetWorkpads({
    page: 1,
    size: 1000 // Set a large number to fetch all records
  });

  const handleRowClick = (row: Row<Workpads>) => {
    const rowId = row.id;
    const newSelection: Record<string, boolean> = {
      [rowId as unknown as number]: !rowSelection[rowId as unknown as number]
    };
    setRowSelection(newSelection);
    setSelectedWorkpadNo(row.original.workpadNo);
  };

  useEffect(() => {
    if (location.state?.workpadNo && data?.content) {
      const workpadIndex = data.content.findIndex((w) => w.workpadNo === location.state.workpadNo);
      if (workpadIndex !== -1) {
        setRowSelection({ [workpadIndex]: true });
        setSelectedWorkpadNo(location.state.workpadNo);
      }
    }
  }, [location.state, data?.content]);

  const handleSortingChange = (newSorting: any) => {
    setSorting(newSorting);
  };

  const handleFilterChange = (newFilters: any) => {
    setColumnFilters(newFilters);
  };

  const handleCreateNewWorkpad = () => {
    if (isCreatingNewWorkpad) return; // Prevent multiple new rows

    const newWorkpad: Partial<Workpads> = {
      workpadNo: 0, // Temporary ID for new row
      workpadType: 1,
      workpadTitle: '',
      closeDate: '',
      checkSumAmt: 0,
      status: 'New',
      changedBy: ''
    };

    setNewWorkpadData(newWorkpad);
    setIsCreatingNewWorkpad(true);
  };

  const handleSaveNewWorkpad = (workpadData: { workpadType: number; workpadTitle: string }) => {
    createWorkpad(workpadData, {
      onSuccess: () => {
        refetch();
        setIsCreatingNewWorkpad(false);
        setNewWorkpadData(null);
      },
      onError: (error) => {
        console.error('Failed to create workpad:', error);
      }
    });
  };

  const handleCancelNewWorkpad = () => {
    setIsCreatingNewWorkpad(false);
    setNewWorkpadData(null);
  };

  // Prepare table data including new workpad row if creating
  const tableData = useMemo(() => {
    const existingData = data?.content || [];
    if (isCreatingNewWorkpad && newWorkpadData) {
      return [newWorkpadData as Workpads, ...existingData];
    }
    return existingData;
  }, [data?.content, isCreatingNewWorkpad, newWorkpadData]);

  return (
    <PageLayout
      leftSidePanel={<Menu primaryMenuOpen={true} primaryMenuContent={<MenuPanel />} breakpoint="md" />}
      rightSidePanel={<ActivityLogPanel partyId={partyId || ''} householdId={''} paymentNo={''} />}
      rightSidebarProps={{ background: 'white', collapsedWidth: 0, brakepoint: 'xl' }}
      widgetTop={<DynamicWidget placeholderId={PagePlaceholderIds.fswWorkpadPageTop} />}
      widgetBottom={<DynamicWidget placeholderId={PagePlaceholderIds.fswWorkpadPageBottom} />}
      breadcrumb={<BreadcrumbsPanel wrapper={(children: React.ReactNode) => <PageLayoutGridWrapper>{children}</PageLayoutGridWrapper>} />}
      alertBar={<AlertBar wrapper={(children: React.ReactNode) => <PageLayoutGridWrapper>{children}</PageLayoutGridWrapper>} />}
    >
      <Box p={2}>
        <Card
          title={t('pages.workpad') || ''}
          isCollapsible={false}
          disablePaddingBottom={true}
          headerElement={
            <Box display="flex" marginLeft="auto" gap="10px">
              <Button spacing="tight" color="secondary">
                {t`workpad.authorization`}
              </Button>
            </Box>
          }
        >
          <>
            <Accordion title={t('workpad.workpadsAccordion') || ''} variant="clean" defaultExpanded={true}>
              <DataTable<Workpads, unknown>
                columns={columns}
                data={tableData}
                tableId="fsw-workpad-items"
                showPagination={false}
                sorting={sorting}
                setSorting={handleSortingChange}
                columnFilters={columnFilters}
                setColumnFilters={handleFilterChange}
                showToolbar={tableOptions.showToolbar}
                showColumnFilters={tableOptions.showColumnFilters}
                manualSorting={tableOptions.manualSorting}
                manualFiltering={tableOptions.manualFiltering}
                enableRowSelection={tableOptions.enableRowSelection}
                rowSelection={rowSelection}
                onRowClick={handleRowClick}
                tableStatusIndicators={{
                  isLoading,
                  isError,
                  errorText: tableOptions.errorText,
                  noRecordsText: tableOptions.noRecordsText
                }}
                rightToolbarSlot={
                  <Box display="flex" gap="10px">
                    <Button
                      spacing="tight"
                      color="secondary"
                      onClick={handleCreateNewWorkpad}
                      disabled={isCreatingNewWorkpad}
                    >
                      {t`workpad.createWorkpad`}
                    </Button>
                    <Button spacing="tight" color="secondary" startIcon={<SaveIcon />}>
                      {t`workpad.save`}
                    </Button>
                    <Button spacing="tight" color="primary">
                      {t`workpad.postItems`}
                    </Button>
                  </Box>
                }
              />
            </Accordion>

            <Accordion title={t('workpad.accountItemsAccordion') || ''} variant="clean" defaultExpanded={true}>
              <AccountItemsTable />
              <Box display="flex" flexDirection="column" alignItems="flex-end" mt={2} pr={2}>
                <Box fontSize="1rem">{t('workpad.workpadBalance') || 'Workpad balance'}</Box>
                <Box fontSize="1.2rem">EUR 3,045.93</Box>
              </Box>
            </Accordion>

            <Accordion title={t('workpad.accountItemsDetails') || ''} variant="clean" defaultExpanded={true}>
              <WorkpadItemDetailsForm formId="workpad-form" />
            </Accordion>
          </>
        </Card>
      </Box>
    </PageLayout>
  );
};
