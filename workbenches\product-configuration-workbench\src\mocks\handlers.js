/* eslint-disable import/no-extraneous-dependencies */
import {
  defValAndFuncConfPage,
  defValAndFuncCustPage,
  defValAndFuncError,
  defValAndFuncFixedPage,
  defValAndFuncFuncPage,
  defValAndFuncNamePage,
  defValAndFuncObjectPage,
  defValAndFuncRiskPage
} from '@mocks/data/defaultValues.js';
import { fieldValidationRules } from '@mocks/data/fieldValidationRules.js';
import { menuActions } from '@mocks/data/menuActions.js';
import { objectColumns } from '@mocks/data/objectColumns.js';
import { objectTypesPage } from '@mocks/data/objectTypes';
import { productLineRisks } from '@mocks/data/productLineRisks.js';
import { productLineTariffCodes } from '@mocks/data/productLineTariffCodes.js';
import { referenceDictionaries } from '@mocks/data/referenceDictionaries.js';
import { riskColumns } from '@mocks/data/riskColumns.js';
import { subobjectColumns } from '@mocks/data/subobjectColumns.js';
import { uiConfigRisk } from '@mocks/data/uiConfigRisk.js';
import { uiConfigurationObjectFields } from '@mocks/data/uiConfigurationObjectFields.js';
import { rest } from 'msw';
import { vi } from 'vitest';

import envLocalJson from '../../local-environment.json';
import { globalTariffStructuresPage } from './data/globalTariffStructures';
import { mainProductLineVersionsPage } from './data/mainProductLineVersions';
import { policylineShortDescriptionsPage } from './data/policylineShortDescriptions';
import { productLineDetailsPage1, productLineDetailsPage2 } from './data/productLineDetails';
import { productLineEntityVersionsPage } from './data/productLineEntityVersions';
import { recentProductLines } from './data/recentProductLines';
import { recentProducts } from './data/recentProducts';
import { recentTariffs } from './data/recentTariffs';
import { subobjectTypesPage } from './data/subobjectTypes';
import { topBar } from './data/topBar';
import { uiConfigRiskPage } from './data/uiConfigriskPage';
import { uiConfigSubobjectPage } from './data/uiConfigSubobjectPage';
import { uiConfigurationsPage } from './data/uiConfigurations';
import { uiConfigurationVersionsPage } from './data/uiConfigurationVersions';
import { usersPage } from './data/users';
import {
  uiBlockFieldType,
  uiBlockObjectDefaultType,
  uiBlockRiskDefaultType,
  uiBlockRiskSize,
  uiBlockRiskType,
  uiBlockType,
  xlaReferencePage,
  xlaYesNo
} from './data/xlaReference';

export const handlersForBrowser = [
  rest.get(`*/recent-products`, (_req, res, ctx) => res(ctx.status(200), ctx.json(recentProducts))),
  rest.get(`*/recent-product-lines`, (_req, res, ctx) => res(ctx.status(200), ctx.json(recentProductLines))),
  rest.get(`*/recent-tariffs`, (_req, res, ctx) => res(ctx.status(200), ctx.json(recentTariffs))),
  rest.get(`*/product-line-details`, (req, res, ctx) => {
    const page = req.url.searchParams.get('page');
    if (page === '2') {
      return res(ctx.status(200), ctx.json(productLineDetailsPage2));
    }
    return res(ctx.status(200), ctx.json(productLineDetailsPage1));
  }),
  rest.get(`*/product-line-details/:prodId/version/:versionId`, (_req, res, ctx) =>
    res(ctx.status(200), ctx.json(productLineDetailsPage1))
  ),
  rest.get(`*/main-product-line-versions`, (_req, res, ctx) => res(ctx.status(200), ctx.json(mainProductLineVersionsPage))),
  rest.get(`*/product-line-entity-versions`, (_req, res, ctx) => res(ctx.status(200), ctx.json(productLineEntityVersionsPage))),
  rest.get(`*/ui-configurations`, (req, res, ctx) => {
    console.log(req);
    return res(ctx.status(200), ctx.json(uiConfigurationsPage));
  }),
  rest.post(`*/ui-configuration-with-blocks`, (_req, res, ctx) => res(ctx.status(200))),
  rest.put(`*/ui-configuration-with-blocks/:id`, (req, res, ctx) => {
    if (req.params.id == '1') {
      return res(ctx.status(404));
    }
    return res(ctx.status(200), ctx.json({ message: 'success' }));
  }),
  rest.delete(`*/ui-configurations/:id`, (_req, res, ctx) => res(ctx.status(200))),
  rest.get(`*/top-bar`, (_req, res, ctx) => res(ctx.status(200), ctx.json(topBar))),
  rest.put(`*/product-line-details/:prodId/versions/:versionId`, (_req, res, ctx) => res(ctx.status(200), ctx.json({}))),
  rest.get(`*/global-tariff-structures`, (_req, res, ctx) => res(ctx.status(200), ctx.json(globalTariffStructuresPage))),
  rest.get(`*/object-types`, (_req, res, ctx) => res(ctx.status(200), ctx.json(objectTypesPage))),
  rest.get(`*/subobject-types`, (_req, res, ctx) => res(ctx.status(200), ctx.json(subobjectTypesPage))),
  rest.get(`*/common/xla-references`, (req, res, ctx) => {
    if (req.url.searchParams.get('referenceName') == 'UI_BLOCK_TYPE') {
      return res(ctx.status(200), ctx.json(uiBlockType));
    }
    return res(ctx.status(200), ctx.json(xlaReferencePage));
  }),
  rest.get(`*/ui-configuration-versions`, (_req, res, ctx) => res(ctx.status(200), ctx.json(uiConfigurationVersionsPage))),
  rest.get(`*/ui-configuration-fields`, (_req, res, ctx) => res(ctx.status(200), ctx.json(uiConfigurationObjectFields))),
  rest.get(`*/object-columns`, (_req, res, ctx) => res(ctx.status(200), ctx.json(objectColumns))),
  rest.get(`*/subobject-columns`, (_req, res, ctx) => res(ctx.status(200), ctx.json(subobjectColumns))),
  rest.get(`*/field-validation-rules`, (_req, res, ctx) => res(ctx.status(200), ctx.json(fieldValidationRules))),
  rest.get(`*/product-line-tariff-codes`, (_req, res, ctx) => res(ctx.status(200), ctx.json(productLineTariffCodes))),
  rest.get(`*/references/reference-dictionaries`, (_req, res, ctx) => res(ctx.status(200), ctx.json(referenceDictionaries))),
  rest.get(`*/ui-configuration-default-values-and-functions`, (req, res, ctx) => {
    if (req.url.searchParams.get('defaultValueType') == 'FIXED') {
      return res(ctx.status(200), ctx.json(defValAndFuncFixedPage));
    }
    if (req.url.searchParams.get('defaultValueType') == 'NAME') {
      return res(ctx.status(200), ctx.json(defValAndFuncNamePage));
    }
    if (req.url.searchParams.get('defaultValueType') == 'CUST') {
      return res(ctx.status(200), ctx.json(defValAndFuncCustPage));
    }
    if (req.url.searchParams.get('defaultValueType') == 'OBJECT') {
      return res(ctx.status(200), ctx.json(defValAndFuncObjectPage));
    }
    if (req.url.searchParams.get('defaultValueType') == 'RISK') {
      return res(ctx.status(200), ctx.json(defValAndFuncRiskPage));
    }
    if (req.url.searchParams.get('defaultValueType') == 'FUNC') {
      return res(ctx.status(200), ctx.json(defValAndFuncFuncPage));
    }
    if (req.url.searchParams.get('defaultValueType') == 'CONFIG_PACKAGE') {
      return res(ctx.status(200), ctx.json(defValAndFuncConfPage));
    }

    //Should not be the case as defaultValueType is mandatory parameter
    return res(ctx.status(200), ctx.json(defValAndFuncError));
  })
];

export const handlersForTests = [
  rest.get(`*/recent-products`, (_req, res, ctx) => res(ctx.status(200), ctx.json(recentProducts))),
  rest.get(`*/recent-product-lines`, (_req, res, ctx) => res(ctx.status(200), ctx.json(recentProductLines))),
  rest.get(`*/recent-tariffs`, (_req, res, ctx) => res(ctx.status(200), ctx.json(recentTariffs))),
  rest.get(`*/product-line-details`, (req, res, ctx) => {
    const page = req.url.searchParams.get('page');
    if (page === '2') {
      return res(ctx.status(200), ctx.json(productLineDetailsPage2));
    }
    return res(ctx.status(200), ctx.json(productLineDetailsPage1));
  }),
  rest.get(`*/product-line-details/:prodId/version/:versionId`, (_req, res, ctx) =>
    res(ctx.status(200), ctx.json(productLineDetailsPage1))
  ),
  rest.get(`*/main-product-line-versions`, (_req, res, ctx) => res(ctx.status(200), ctx.json(mainProductLineVersionsPage))),
  rest.get(`*/users`, (_req, res, ctx) => res(ctx.status(200), ctx.json(usersPage))),
  rest.get(`*/product-line-entity-versions`, (_req, res, ctx) => res(ctx.status(200), ctx.json(productLineEntityVersionsPage))),
  rest.get(`*/ui-configurations`, (_req, res, ctx) => res(ctx.status(200), ctx.json(uiConfigurationsPage))),
  rest.post(`*/ui-configuration-with-blocks`, (_req, res, ctx) => res(ctx.status(200))),
  rest.put(`*/ui-configuration-with-blocks/:id`, (req, res, ctx) => {
    if (req.params.id == '1') {
      return res(ctx.status(404));
    }
    return res(ctx.status(200), ctx.json({ message: 'success' }));
  }),
  rest.delete(`*/ui-configurations/:id`, (_req, res, ctx) => res(ctx.status(200))),
  rest.get(`*/top-bar`, (_req, res, ctx) => res(ctx.status(200), ctx.json(topBar))),
  rest.get(`*/local-environment.json`, (_req, res, ctx) => res(ctx.status(200), ctx.json(envLocalJson))),
  rest.put(`*/product-line-details/:prodId/versions/:versionId`, (_req, res, ctx) => res(ctx.status(200), ctx.json({}))),
  rest.get(`*/global-tariff-structures`, (_req, res, ctx) => res(ctx.status(200), ctx.json(globalTariffStructuresPage))),
  rest.get(`*/object-types`, (_req, res, ctx) => res(ctx.status(200), ctx.json(objectTypesPage))),
  rest.get(`*/subobject-types`, (_req, res, ctx) => res(ctx.status(200), ctx.json(subobjectTypesPage))),
  rest.get(`*/policy-line-short-descriptions`, (_req, res, ctx) => res(ctx.status(200), ctx.json(policylineShortDescriptionsPage))),
  rest.get(`*/common/xla-references`, (req, res, ctx) => {
    if (req.url.searchParams.get('referenceName') == 'UI_BLOCK_TYPE') {
      return res(ctx.status(200), ctx.json(uiBlockType));
    }
    if (req.url.searchParams.get(`referenceName`) == `UI_FIELD_SIZE`) {
      return res(ctx.status(200), ctx.json(uiBlockRiskSize));
    }
    if (req.url.searchParams.get(`referenceName`) == `NUMBER_FORMAT_TYPE`) {
      return res(ctx.status(200), ctx.json(uiBlockRiskType));
    }
    if (req.url.searchParams.get(`referenceName`) == `RISK_DEFAULT_TYPE`) {
      return res(ctx.status(200), ctx.json(uiBlockRiskDefaultType));
    }
    if (req.url.searchParams.get(`referenceName`) == `YESNO`) {
      return res(ctx.status(200), ctx.json(xlaYesNo));
    }
    if (req.url.searchParams.get(`referenceName`) == `UI_FIELD_TYPE`) {
      return res(ctx.status(200), ctx.json(uiBlockFieldType));
    }
    if (req.url.searchParams.get(`referenceName`) == `OBJ_DEFAULT_TYPE`) {
      return res(ctx.status(200), ctx.json(uiBlockObjectDefaultType));
    }
    return res(ctx.status(200), ctx.json(xlaReferencePage));
  }),
  rest.get(`*/ui-configuration-versions`, (_req, res, ctx) => res(ctx.status(200), ctx.json(uiConfigurationVersionsPage))),
  rest.get(`*/ui-configuration-fields`, (_req, res, ctx) => {
    if (_req.url.searchParams.get(`uiConfigurationBlockName`) == `RISK`) {
      return res(ctx.status(200), ctx.json(uiConfigRiskPage));
    }
    if (_req.url.searchParams.get(`uiConfigurationBlockName`) == `OBJECT`) {
      return res(ctx.status(200), ctx.json(uiConfigurationObjectFields));
    }
    if (_req.url.searchParams.get(`uiConfigurationBlockName`) == `EQUIPMENT`) {
      return res(ctx.status(200), ctx.json(uiConfigSubobjectPage));
    }
    return res(ctx.status(404));
  }),
  rest.get(`*/object-columns`, (_req, res, ctx) => res(ctx.status(200), ctx.json(objectColumns))),
  rest.get(`*/subobject-columns`, (_req, res, ctx) => res(ctx.status(200), ctx.json(subobjectColumns))),
  rest.get(`*/field-validation-rules`, (_req, res, ctx) => res(ctx.status(200), ctx.json(fieldValidationRules))),
  rest.get(`*/product-line-tariff-codes`, (_req, res, ctx) => res(ctx.status(200), ctx.json(productLineTariffCodes))),
  rest.get(`*/references/reference-dictionaries`, (_req, res, ctx) => res(ctx.status(200), ctx.json(referenceDictionaries))),
  rest.get(`*/menu-actions`, (_req, res, ctx) => res(ctx.status(200), ctx.json(menuActions))),
  rest.get(`*/risk-columns`, (_req, res, ctx) => res(ctx.status(200), ctx.json(riskColumns))),
  rest.put(`*/ui-configuration-fields/:id`, (_req, res, ctx) => res(ctx.status(200))),
  rest.post(`*/ui-configuration-fields`, (_req, res, ctx) => res(ctx.status(200))),
  rest.delete(`*/ui-configuration-fields/:id`, (_req, res, ctx) => res(ctx.status(200))),
  rest.get(`*/product-line-risks`, (_req, res, ctx) => res(ctx.status(200), ctx.json(productLineRisks))),
  rest.get(`*/ui-configuration-risks-with-fields`, (_req, res, ctx) => res(ctx.status(200), ctx.json(uiConfigRisk))),
  rest.delete(`*/ui-configuration-risks-with-fields/:id`, (_req, res, ctx) => res(ctx.status(200))),
  rest.get(`*/ui-configuration-default-values-and-functions`, (req, res, ctx) => {
    if (req.url.searchParams.get('defaultValueType') == 'FIXED') {
      return res(ctx.status(200), ctx.json(defValAndFuncFixedPage));
    }
    if (req.url.searchParams.get('defaultValueType') == 'NAME') {
      return res(ctx.status(200), ctx.json(defValAndFuncNamePage));
    }
    if (req.url.searchParams.get('defaultValueType') == 'CUST') {
      return res(ctx.status(200), ctx.json(defValAndFuncCustPage));
    }
    if (req.url.searchParams.get('defaultValueType') == 'OBJECT') {
      return res(ctx.status(200), ctx.json(defValAndFuncObjectPage));
    }
    if (req.url.searchParams.get('defaultValueType') == 'RISK') {
      return res(ctx.status(200), ctx.json(defValAndFuncRiskPage));
    }
    if (req.url.searchParams.get('defaultValueType') == 'FUNC') {
      return res(ctx.status(200), ctx.json(defValAndFuncFuncPage));
    }
    if (req.url.searchParams.get('defaultValueType') == 'CONFIG_PACKAGE') {
      return res(ctx.status(200), ctx.json(defValAndFuncConfPage));
    }

    //Should not be the case as defaultValueType is mandatory parameter
    return res(ctx.status(200), ctx.json(defValAndFuncError));
  }),
  rest.post('*/v1/traces', (_req, res, ctx) => {
    return tracesMockFunction(_req, res, ctx);
  }),
  rest.get('*/api/test', (_req, res, ctx) => {
    const traceparent = _req.headers.get('traceparent') ?? undefined;
    return res(ctx.status(200), ctx.set('traceparent', traceparent ?? ''), ctx.json({ ok: true }));
  })
];

export const tracesMockFunction = vi.fn((_req, res, ctx) => {
  return res(ctx.json({ partialSuccess: {} }));
});
