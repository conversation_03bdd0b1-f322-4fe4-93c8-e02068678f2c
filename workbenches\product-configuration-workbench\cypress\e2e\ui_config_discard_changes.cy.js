import '../support/commands.ts';

describe('PCW E2E test: Discard UI changes', () => {
  beforeEach(() => {
    // Login to Keycloak
    cy.loginWithKeycloak();
    // Cypress starts out with a blank slate for each test
    // so we must tell it to visit our website with the `cy.visit()` command.
    cy.visit(Cypress.env('base_url') + '/pcw/landing');
  });

  afterEach(() => {
    //cy.logoutWithKeycloak();
  });

  const config = {
    connectionString: Cypress.env('connectionString'),
    user: Cypress.env('user'),
    password: Cypress.env('password')
  };

  const inputProductLineID = 'G2';
  const inputProductLineIdVersion = '1';

  it('check Discard changes functionality when updating UI Config', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();

    //verify BULK UI config
    cy.get('[data-testid="ui-config-name-filter-input"] > :first-child').type('BULK_G2');
    cy.get('[data-testid="ui-config-description-input"] > :first-child').should('have.value', 'UI Configuration for bulk update');
    cy.get('[data-testid="block-description-input"] input').eq(0).should('have.value', 'Object Information');
    cy.get('[data-testid="block-sort-no-input"] input').eq(0).should('have.value', '1');
    cy.get('[data-testid="block-expanded-checkbox"]').eq(0).find('svg#checked-icon').should('exist');
    cy.get('[data-testid="block-read-only-checkbox"]').eq(0).find('svg#unchecked-icon').should('exist');

    //change data in BULK UI config
    cy.get('[data-testid="ui-config-description-input"]').type(' update');
    cy.get('[data-testid="block-description-input"] input').eq(0).type(' update');
    cy.get('[data-testid="block-sort-no-input"]').eq(0).clear().type('7');
    cy.get('[data-testid="block-expanded-checkbox"] > :first-child').eq(0).uncheck({ force: true });
    cy.get('[data-testid="block-read-only-checkbox"] > :first-child').eq(0).check({ force: true });

    cy.wait(1000);
    cy.get('[data-testid="risk-and-objects-tab"]').click();
    //verify modal text
    cy.get('[data-testid="modal"] .title-container .title').should('contain.text', 'Discard changes');

    //click keep-editing changes button
    cy.get('[data-testid="pending-cancel-button-id"]').click();
    //verify that changes are still present
    cy.get('[data-testid="ui-config-description-input"] > :first-child').should('have.value', 'UI Configuration for bulk update update');
    cy.get('[data-testid="block-description-input"] input').eq(0).should('have.value', 'Object Information update');
    cy.get('[data-testid="block-sort-no-input"] input').eq(0).should('have.value', '7');
    cy.get('[data-testid="block-expanded-checkbox"]').eq(0).find('svg#unchecked-icon').should('exist');
    cy.get('[data-testid="block-read-only-checkbox"]').eq(0).find('svg#checked-icon').should('exist');

    cy.wait(1000);
    cy.get('[data-testid="risk-and-objects-tab"]').click();
    //verify modal text
    cy.get('[data-testid="modal"] .title-container .title').should('contain.text', 'Discard changes');
    //click discard changes button
    cy.get('[data-testid="pending-discard-button-id"]').click();
    //go back to UI onfig tab
    cy.get('[data-testid="ui-configuration-tab"]').click();

    //verify that changes were discarded
    cy.get('[data-testid="ui-config-name-filter-input"] > :first-child').type('BULK_G2');
    cy.get('[data-testid="ui-config-description-input"] > :first-child').should('have.value', 'UI Configuration for bulk update');
    cy.get('[data-testid="block-description-input"] input').eq(0).should('have.value', 'Object Information');
    cy.get('[data-testid="block-sort-no-input"] input').eq(0).should('have.value', '1');
    cy.get('[data-testid="block-expanded-checkbox"]').eq(0).find('svg#checked-icon').should('exist');
    cy.get('[data-testid="block-read-only-checkbox"]').eq(0).find('svg#unchecked-icon').should('exist');
  });

  it('check Discard changes functionality when deleting  UI Config block', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();

    //filter BULK UI config
    cy.get('[data-testid="ui-config-name-filter-input"] > :first-child').type('BULK_G2');
    //verify that UI block Object exist
    cy.get('[data-testid="block-name-cell-0"]').contains('OBJECT');
    //delete that UI block Object
    cy.get('[data-testid="DeleteIcon"]').eq(1).click();

    cy.wait(1000);
    cy.get('[data-testid="delete-modal-confirm-button"]').click();
    cy.get('[data-testid="risk-and-objects-tab"]').click();
    //verify modal text
    cy.get('[data-testid="modal"] .title-container .title').should('contain.text', 'Discard changes');

    //click keep-editing changes button
    cy.get('[data-testid="pending-cancel-button-id"]').click();
    //verify that UI block OBJECT does not  exist
    //cy.get('[data-testid="block-name-cell-0"]').should('not.contain', 'OBJECT');
    cy.get('[data-testid="block-name-cell-0"]').should('not.exist');

    cy.wait(1000);
    cy.get('[data-testid="risk-and-objects-tab"]').click();
    //verify modal text
    cy.get('[data-testid="modal"] .title-container .title').should('contain.text', 'Discard changes');

    //click discard changes button
    cy.get('[data-testid="pending-discard-button-id"]').click();
    //go back to UI onfig tab
    cy.get('[data-testid="ui-configuration-tab"]').click();
    //verify BULK UI config
    cy.get('[data-testid="ui-config-name-filter-input"] > :first-child').type('BULK_G2');
    //verify that UI block Object exist
    cy.get('[data-testid="block-name-cell-0"]').contains('OBJECT');
  });

  it('check Discard changes functionality when adding New UI Config', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();
    //create New UI config
    cy.get('[data-testid="add-new-ui-config-button"]').click();
    
    //cy.get('[data-testid="ui-config-name-input"]').type('aaa', { force: true });
    cy.get('[data-testid="ui-config-name-input"] input').type('ccc', { force: true });


    cy.get('[data-testid="ui-config-description-input"]').eq(0).type('test description');
    cy.get('[data-testid="ui-config-object-type-select"]').click();
    cy.get('[data-value="G2"]').click();

    cy.wait(1000);
    cy.get('[data-testid="risk-and-objects-tab"]').click();
    //verify modal text
    cy.get('[data-testid="modal"] .title-container .title').should('contain.text', 'Discard changes');

    //click keep-editing changes button
    cy.get('[data-testid="pending-cancel-button-id"]').click();
    //verify UI config main data still present
    cy.get('[data-testid="ui-config-name-input"] input').should('have.value', 'ccc');
    cy.get('[data-testid="ui-config-description-input"] input').eq(0).should('have.value', 'test description');
    cy.get('[data-testid="ui-config-object-type-select"] input').should('have.value', 'G2');

    cy.wait(1000);
    cy.get('[data-testid="risk-and-objects-tab"]').click();
    //verify modal text
    cy.get('[data-testid="modal"] .title-container .title').should('contain.text', 'Discard changes');
    //click discard changes button
    cy.get('[data-testid="pending-discard-button-id"]').click();
    //go back to UI onfig tab
    cy.get('[data-testid="ui-configuration-tab"]').click();
    //verify AAA UI config
    cy.get('[data-testid="ui-config-name-filter-input"] > :first-child').type('aaa');
    cy.get('.MuiTypography-root.MuiTypography-default').should('include.text', 'No UI configurations');
  });
});
