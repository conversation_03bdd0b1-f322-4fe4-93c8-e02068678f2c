path: /v1/ui-configuration-with-blocks/{uiConfigId}
method: put
flow: pcwUpdateUIConfigurationWithBlocks.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
definition:
  operationId: pcwUpdateUIConfigurationWithBlocks
  tags:
    - pcw
  summary: ""
  description: ""
  parameters:
    - in: path
      name: uiConfigId
      required: true
      description: ""
      schema:
        type: number
  requestBody:
    description: ""
    content:
      application/json:
        schema:
          $ref: pcwUpdateUiConfigurationWithBlocksRequest.yaml
  responses: {}
