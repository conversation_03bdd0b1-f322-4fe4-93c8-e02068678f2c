path: /v1/ui-configuration-risks-with-fields
method: get
flow: pcwGetUIconfigurationRisksWithFields.yaml
secured: false
errorHandlers:
  - PCW_NOT_FOUND.yaml
  - PCW_BAD_REQUEST.yaml
  - PCW_UNAUTHORIZED.yaml
definition:
  operationId: pcwGetUIconfigurationRisksWithFields
  tags:
    - pcw
  summary: ""
  description: ""
  parameters:
    - in: query
      name: productLineId
      required: true
      description: Product Line Id to search for.
      schema:
        type: string
    - in: query
      name: productLineVersion
      required: true
      description: Product Line Version to search for.
      schema:
        type: number
    - in: query
      name: "uiConfigurationName "
      required: true
      description: Name of Product Line UI Configuration to search for.
      schema:
        type: string
    - in: query
      name: "uiConfigurationBlockName "
      required: true
      description: Name of UI Configuration Block to search for.
      schema:
        type: string
    - in: query
      name: riskNo
      required: false
      description: Risk number to search for.
      schema:
        type: number
    - in: query
      name: defaultSelected
      required: false
      description: Risk selected by default to search for.
      schema:
        type: boolean
    - in: query
      name: defaultExpanded
      required: false
      description: Risk expanded by default to search for.
      schema:
        type: boolean
    - in: query
      name: expandSelected
      required: false
      description: Risk is expanded by default if selected to search for.
      schema:
        type: boolean
    - in: query
      name: mandatory
      required: false
      description: Risk is mandatory to search for.
      schema:
        type: boolean
    - in: query
      name: sort
      required: false
      description: "Format: property(:asc|desc). Default order is ascending. If no
        sort parameter is provided, data is sorted by sortNo in ascending
        order.  Usage example: ...?sort=columnName:desc  The following
        attributes can be used for sorting:  columnName, columnLabel, sortNo."
      schema:
        type: string
    - in: query
      name: page
      required: false
      description: "Result page you want to retrieve (1..N)  Example : 1"
      schema:
        type: number
    - in: query
      name: size
      required: false
      description: "Number of records per page  Example : 25"
      schema:
        type: number
    - in: query
      name: riskNoOrDescription
      required: false
      description: Search by risk number or risk description
      schema:
        type: string
  responses: {}
