import { config, OTelProvider } from '@sapiens/workbench-feature-library';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React, { FC } from 'react';
import { AuthProvider, AuthProviderProps } from 'react-oidc-context';
import { Provider } from 'react-redux';

import App from './App';
import { store } from './store/store';
import TranslationProvider from './translations/TranslantionProvider';

const getConfig = (): AuthProviderProps => {
  return {
    authority: config.authDomain,
    client_id: config.authClientId,
    redirect_uri: window.location.href,
    response_type: 'code',
    scope: config.authScope,
    redirectMethod: 'replace',
    metadataUrl: config.authDomain,
    client_authentication: 'client_secret_post',
    metadata: {
      token_endpoint: config.authTokenDomain,
      authorization_endpoint: config.authDomain,
      end_session_endpoint: config.authLogoutDomain,
      userinfo_endpoint: config.authUserInfo
    },
    extraQueryParams: { audience: config.authAudience },
    onSigninCallback: (): void => {
      window.history.replaceState({}, document.title, window.location.pathname);
    },
    loadUserInfo: false
  };
};

const InitializeApp: FC = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: 1000 * 60, // 60 seconds
        refetchOnMount: true,
        refetchOnWindowFocus: 'always',
        refetchOnReconnect: 'always',
        suspense: false
      },
      mutations: {
        retry: false
      }
    }
  });

  const oidcConfig = getConfig();
  const traceHeaderCorsUrlsConfig = config.otelTracesHeaderCorsUrls ?? config.gatewayBaseUrl;
  const propagateTraceHeaderCorsUrls = traceHeaderCorsUrlsConfig?.split(',').map((urlPattern: string) => new RegExp(urlPattern)) ?? [];

  return (
    <React.StrictMode>
      <OTelProvider
        config={{
          serviceName: config.otelTracesServiceName,
          collectorEndpoint: config.otelTracesCollectorEndpoint,
          instrumentation: {
            type: config.otelEnableinstrumentation,
            propagateTraceHeaderCorsUrls
          }
        }}
      >
        <AuthProvider {...oidcConfig}>
          <Provider store={store}>
            <TranslationProvider>
              <QueryClientProvider client={queryClient}>
                <App />
              </QueryClientProvider>
            </TranslationProvider>
          </Provider>
        </AuthProvider>
      </OTelProvider>
    </React.StrictMode>
  );
};

export default InitializeApp;
