path: /v1/ui-configuration-versions
method: get
flow: pcwGetUiConfigurationVersions.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
definition:
  operationId: pcwGetUiConfigurationVersions
  tags:
    - pcw
  summary: Fetches UI configuration versions from Tia
  description: ""
  parameters:
    - in: query
      name: productLineId
      required: true
      description: Product Line Id to search for.
      schema:
        type: string
    - in: query
      name: uiConfigName
      required: false
      description: Name of Product Line UI Configuration to search for. Partial search
        supported.
      schema:
        type: string
    - in: query
      name: uiConfigDescription
      required: false
      description: Description of Product Line UI Configuration to search for. Partial
        search supported.
      schema:
        type: string
    - in: query
      name: uiConfigVersion
      required: false
      description: Version of Product Line UI Configuration to search for.
      schema:
        type: string
    - in: query
      name: partialVersionSearch
      required: false
      description: Partial product line version number search. When true, search is
        done as text on formatted (eg. 1.1) and database (eg. 1.001) version
        numbers matching from left to right. Default is false.
      schema:
        type: boolean
    - in: query
      name: page
      required: false
      description: "Result page you want to retrieve (1..N)  Example : 1"
      schema:
        type: number
    - in: query
      name: size
      required: false
      description: "Number of records per page  Example : 25"
      schema:
        type: number
  responses: {}
