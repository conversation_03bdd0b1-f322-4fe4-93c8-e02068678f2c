import '../support/commands.ts';

describe('PCW E2E test: CRUD subobject Ui config', () => {
  beforeEach(() => {
    // Login to Keycloak
    cy.loginWithKeycloak();
    // Cypress starts out with a blank slate for each test
    // so we must tell it to visit our website with the `cy.visit()` command.
    cy.visit(Cypress.env('base_url') + '/pcw/landing');
  });

  afterEach(() => {
    //cy.logoutWithKeycloak();
  });

  const config = {
    connectionString: Cypress.env('connectionString'),
    user: Cypress.env('user'),
    password: Cypress.env('password')
  };

  const inputProductLineID = 'G2';
  const inputProductLineIdVersion = '1';

  it('CRUD UI configuration', () => {
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    //assertion about UI configuration page
    cy.verifyUiConfigurationPage();

    //create New UI config
    cy.get('[data-testid="add-new-ui-config-button"]').click();
    cy.wait(1000);
    cy.get('[data-testid="ui-config-name-input"] input').type('sub', { force: true });
    cy.get('[data-testid="ui-config-description-input"]').eq(0).type('test description');
    cy.get('[data-testid="ui-config-object-type-select"]').click();
    cy.get('[data-value="G2"]').click();
    cy.get('[data-testid="save-button"]').first().click();
    //cy.get('[data-testid="ui-config-success-alert"]').should('be.visible').contains('Changes were saved.');
    cy.contains('.MuiTypography-root.MuiTypography-medium', 'Success Changes were saved.').should('be.visible');
    cy.wait(1000);
    cy.get('[data-testid="risk-and-objects-tab"]').click();
    cy.get('[data-testid="ui-configuration-tab"]').click();
    cy.filterUIConfig('sub');
    //create new UI subobject block
    cy.get('[data-testid="new-block-button"]').eq(0).click();
    cy.get('[data-testid="block-name-input"]').type('test name3');
    cy.get('[data-testid="block-description-input"]').eq(0).type('test description3');
    cy.get('[data-testid="block-type-select"]').eq(0).click();
    cy.get('[data-value="OBJSLAVE"]').click();
    cy.get('[data-testid="block-obj-slave-type-id-select"]').eq(0).click();
    cy.get('[data-value="G2_DRIVER"]').click();
    cy.get('[data-testid="block-sort-no-input"]').eq(0).type('17');
    cy.get('[data-testid="save-button"]').first().click();
    //cy.get('[data-testid="ui-config-success-alert"]').should('be.visible').contains('Changes were saved.');
    cy.contains('.MuiTypography-root.MuiTypography-medium', 'Success Changes were saved.').should('be.visible');

    cy.wait(1000);
    cy.get('[data-testid="risk-and-objects-tab"]').click();
    cy.get('[data-testid="ui-configuration-tab"]').click();
    // navigate to object
    cy.filterUIConfig('sub');
    cy.get('[data-testid="block-name-cell-0"] a').first().click();
    // Verify that the button with text "Add field" exists
    cy.contains('button', 'Add field').should('exist');
    //Click on Add field button
    cy.clickButtonWithText('Add field');

    cy.wait(1000);
  
    //select Column name
    cy.selectColumnName('EXPIRES D01');
    //enter subobject Order no
    cy.enterSubobjectOrderValue('1');
    //check mandatory checkbox
    //cy.checkSubobjectMandatoryCheckbox();
    //check read-only checkbox
    //cy.checkSubobjectReadOnlyCheckbox();
    //expand Default value type dropdown and choose value
    cy.chooseDefaultValueType('Fixed value');
    //enter Default value
    cy.enterDefaultValue('111');
    //choose LOV type
    cy.chooseSubobjectLovType('TARIFF_CODE');
    //choose LOV
    cy.chooseSubobjectLov('BONUS');
    //enter validation rule
    cy.enterValidationRule('Validate');
    //save object UI config
    cy.saveUIConfig();
    cy.wait(5000);
    //verify msg
    cy.verifySuccessMessage();

    // Find the link with the text "Product Line Details" and click it
    cy.contains('a', 'Product Line Details').click();
    // Click on UI config tab
    cy.get('[data-testid="ui-configuration-tab"]').click();
    // navigate to object
    cy.filterUIConfig('sub');
    cy.get('[data-testid="block-name-cell-0"] a').first().click();
    cy.wait(1000); // Adjust the wait time if necessary

    // Verify fieds////////////////////////////////////////////////
    // verify Column Name
    cy.verifyColumnName('EXPIRES D01');
    //verify Order no
    cy.verifySubobjectOrderValue('1');
    //verify Mandatory checked
   // cy.verifyCheckboxChecked('UiConfigSubObjectFieldMandatory', true);
    //verify read-only checked
    //cy.verifyCheckboxChecked('UiConfigSubObjectFieldReadOnly', true);
    //verify default value type
    cy.verifyDefaultValueType('Fixed value');
    //verify default value
    cy.verifyDefaultValue('111');
    //verify LOV type
    cy.verifySubobjectLovType('TARIFF_CODE');
    //verify LOV
    cy.verifySubobjectLovValue('BONUS Bonus steps');
    //verify validation rule
    cy.verifyValidationRule('Validate mail address');

    ///Update data/////////////////////////////////////////////////
    cy.enterSubobjectOrderValue('2');
    //uncheck mandatory checkbox
    //cy.uncheckCheckbox('UiConfigSubObjectFieldMandatory');
    //uncheck read-only checkbox
    //cy.uncheckCheckbox('UiConfigSubObjectFieldReadOnly');
    //expand Default value type dropdown and choose value
    cy.chooseDefaultValueType('Fixed value');
    //enter Default value
    cy.enterDefaultValue('222');
    //choose LOV type
    cy.chooseSubobjectLovType('TASK_FLOW');
    //choose LOV
    cy.chooseSubobjectLov('F0212 Vehicles');
    //enter validation rule
    cy.enterValidationRule('Mobile');
    //save object UI config
    cy.saveUIConfig();
    //verify msg
    cy.verifySuccessMessage();
    cy.wait(5000);

    // Find the link with the text "Product Line Details" and click it
    cy.contains('a', 'Product Line Details').click();
    // Click on UI config tab
    cy.get('[data-testid="ui-configuration-tab"]').click();
    // navigate to object
    cy.filterUIConfig('sub');
    cy.get('[data-testid="block-name-cell-0"] a').first().click();
    cy.wait(1000); // Adjust the wait time if necessary

    // Verify data/////////////////////////////////////////////
    // verify Column Name
    cy.verifyColumnName('EXPIRES D01');
    //verify Order no
    cy.verifySubobjectOrderValue('2');
    //verify Mandatory checked
    cy.verifyCheckboxChecked('UiConfigSubObjectFieldMandatory', false);
    //verify read-only checked
    cy.verifyCheckboxChecked('UiConfigSubObjectFieldReadOnly', false);
    //verify default value type
    cy.verifyDefaultValueType('Fixed value');
    //verify default value
    cy.verifyDefaultValue('222');
    //verify LOV type
    cy.verifySubobjectLovType('TASK_FLOW');
    //verify LOV
    cy.verifySubobjectLovValue('F0212 Vehicles');
    //verify validation rule
    cy.verifyValidationRule('Mobile phone validation in format +xxx xxx xxx xxx');

    //delete Object UI config field////////////////////////////////////
    cy.get('[data-testid="DeleteIcon"]').click();
    // Click on the button with the test ID "delete-modal-confirm-button"
    cy.get('[data-testid="delete-modal-confirm-button"]').click();
    //verify  Add field for configuration is displayed
    cy.contains('p', 'Add fields for configuration').should('be.visible');
    // Find the link with the text "Product Line Details" and click it
    cy.contains('a', 'Product Line Details').click();
    // Click on UI config tab
    cy.get('[data-testid="ui-configuration-tab"]').click();
    // navigate to object
    cy.filterUIConfig('sub');
    //delete UI config
    cy.get('[data-testid="DeleteIcon"]').eq(0).click();
    cy.get('[data-testid="delete-modal-confirm-button"]').click();
    //click on approve deletion
    //cy.get('[data-testid="ui-config-success-alert"]').should('be.visible').contains('success');
    cy.get('.MuiTypography-medium').should('contain.text', 'Success Data was deleted successfully.').should('be.visible');
  });
});
