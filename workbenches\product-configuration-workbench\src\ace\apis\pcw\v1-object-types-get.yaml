path: /v1/object-types
method: get
flow: pcwGetObjectTypes.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_NOT_FOUND.yaml
  - PCW_UNAUTHORIZED.yaml
definition:
  operationId: pcwGetObjectTypes
  tags:
    - pcw
  summary: ""
  description: ""
  parameters:
    - in: query
      name: productLineId
      required: false
      description: Product line identification.
      schema:
        type: string
    - in: query
      name: productLineVerNo
      required: false
      description: Product line version number.
      schema:
        type: string
    - in: query
      name: agreementLineId
      required: false
      description: Agreement line version ID.
      schema:
        type: number
    - in: query
      name: objectTypeId
      required: false
      description: Object type ID.
      schema:
        type: string
    - in: query
      name: objectTypeName
      required: false
      description: Object type name. Partial text search.
      schema:
        type: string
    - in: query
      name: page
      required: false
      description: Result page you want to retrieve (1..N)
      schema:
        type: string
    - in: query
      name: size
      required: false
      description: Number of records per page
      schema:
        type: string
  responses: {}
