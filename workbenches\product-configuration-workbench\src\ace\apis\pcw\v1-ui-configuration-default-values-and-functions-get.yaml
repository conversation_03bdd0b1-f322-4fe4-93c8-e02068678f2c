path: /v1/ui-configuration-default-values-and-functions
method: get
flow: pcwGetUiConfigurationDefaultValuesAndFunctions.yaml
secured: false
errorHandlers:
  - PCW_NOT_FOUND.yaml
  - PCW_BAD_REQUEST.yaml
  - PCW_UNAUTHORIZED.yaml
definition:
  operationId: pcwGetUiConfigurationDefaultValuesAndFunctions
  tags:
    - pcw
  summary: ""
  description: ""
  parameters:
    - in: query
      name: defaultValueType
      required: true
      description: "Default value type. Possible values:  FIXED - Nothing is returned.
        This option is for a manually entered fixed default value. NAME - List
        of column and item names taken from NAME table. CUST - List of column
        and item names taken from CUSTOMER table. OBJECT - List of column and
        item names taken from OBJECT table. RISK - List of column and item names
        taken from OBJ_RISK_SLAVE table. FUNC - List of user functions stored
        within database package UF_CONFIG_FUNC. CONFIG_PACKAGE - List of all
        user functions and procedures stored within database package
        UF_CONFIG_FUNC."
      schema:
        type: string
    - in: query
      name: itemName
      required: false
      description: Item name. Partial text search.
      schema:
        type: string
    - in: query
      name: page
      required: false
      description: "Result page you want to retrieve (1..N)  Example : 1"
      schema:
        type: number
    - in: query
      name: size
      required: false
      description: "Number of records per page  Example : 25"
      schema:
        type: number
  responses: {}
