path: /v1/object-columns
method: get
flow: pcwGetObjectColumns.yaml
secured: false
errorHandlers:
  - PCW_BAD_REQUEST.yaml
  - PCW_UNAUTHORIZED.yaml
  - PCW_NOT_FOUND.yaml
definition:
  operationId: pcwGetObjectColumns
  tags:
    - pcw
  summary: ""
  description: ""
  parameters:
    - in: query
      name: productLineId
      required: true
      description: Product line identification to search for.
      schema:
        type: string
    - in: query
      name: productLineVersion
      required: true
      description: Product line version to search for.
      schema:
        type: number
    - in: query
      name: objectTypeId
      required: true
      description: Object type id to search for.
      schema:
        type: string
    - in: query
      name: columnName
      required: false
      description: Object column name to search for. Partial text search supported.
      schema:
        type: string
    - in: query
      name: itemName
      required: false
      description: Object item name to search for. Partial text search supported.
      schema:
        type: string
    - in: query
      name: page
      required: false
      description: "Result page you want to retrieve (1..N)  Example : 1"
      schema:
        type: number
    - in: query
      name: size
      required: false
      description: "Number of records per page  Example : 25"
      schema:
        type: number
  responses: {}
