path: /v1/common/xla-references
method: get
flow: pcwXlaReference.yaml
secured: false
errorHandlers:
  - PCW_UNAUTHORIZED.yaml
  - PCW_BAD_REQUEST.yaml
  - PCW_NOT_FOUND.yaml
definition:
  operationId: pcwXlaReference
  tags:
    - pcw
  summary: ""
  description: ""
  parameters:
    - in: query
      name: referenceName
      required: false
      description: ""
      schema:
        type: string
    - in: query
      name: code
      required: false
      description: ""
      schema:
        type: string
    - in: query
      name: page
      required: false
      description: ""
      schema:
        type: number
    - in: query
      name: size
      required: false
      description: ""
      schema:
        type: number
  responses: {}
