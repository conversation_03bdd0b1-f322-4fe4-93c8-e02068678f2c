import '../support/commands.ts';
import { Database } from '../../../cypress-utilities/cypress/index';
import { productLineMainDataGv, productLineMainData44, productLineMainDataTbui } from '../fixtures/constants.cy.ts';

describe('PCW E2E test: view mode in Object window', () => {
  beforeEach(() => {
    // Login to Keycloak
    cy.loginWithKeycloak();
    // Cypress starts out with a blank slate for each test
    // so we must tell it to visit our website with the `cy.visit()` command.
    cy.visit(Cypress.env('base_url') + '/pcw/landing');
  });

  afterEach(() => {
    //cy.logoutWithKeycloak();
  });

  const config = {
    connectionString: Cypress.env('connectionString'),
    user: Cypress.env('user'),
    password: Cypress.env('password')
  };

  const inputProductLineID = 'G2';
  const inputProductLineIdVersion = '1';

  it('Testing Object view mode window', () => {
    //assertion about UI configuration page
    cy.navigateToUiConfigurationPage(inputProductLineID, inputProductLineIdVersion);
    cy.verifyUiConfigurationPage();

    //find needed Subobject and enter the window
    cy.get('[data-testid="ui-config-name-filter-input"]').type('ADF').type('{enter}');
    cy.get('[data-testid="ui-config-name-cell"]').first().should('include.text', 'ADF');
    cy.get('[data-testid="block-name-cell-1"] a').should('be.visible').click();

    //assert all general Subobject window items
    cy.get('[data-testid="breadcrumbs"]').should('be.visible').should('include.text', 'Object UI Configuration');
    cy.contains('span', 'Object UI configuration ').should('be.visible');
    cy.contains('span', '| G2 G2 with extended object').should('be.visible');
    cy.contains('span', 'OBJECT');
    cy.get('[type="submit"]').should('be.visible');

    //assert Field card
    cy.get('[data-testid="ui-configuration-object-fields-container"]').find('span').contains('Field').should('be.visible');
    cy.get('[data-testid="AddCircleIcon"]').should('be.visible');
    cy.get('input[name="sortNo"]').should('be.visible');
    cy.get('input[name="columnName"]').should('be.visible');
    cy.get('input[name="columnLabel"]').should('be.visible');

    //assert Configuation card
    cy.get('#UiObjectFieldConfigurationForm').should('be.visible');
    cy.get('[data-testid="field-type"]').should('be.visible');
    cy.get('#column-name-dropdown-searchable').should('be.visible');
    cy.get('[data-testid="sort-no"]').should('be.visible');
    cy.get('[data-testid="length"]').should('be.visible');
    cy.get('[data-testid="rows"]').should('be.visible');
    cy.get('[data-testid="mandatory"]').should('be.visible');
    cy.get('[data-testid="read-only"]').should('be.visible');
    cy.get('[data-testid="display-condition"]').should('be.visible');
    cy.get('#function-dropdown-searchable').should('be.visible');
    cy.get('[data-testid="default-value-type"]').should('be.visible');
    cy.get('[data-testid="default-value"]').should('be.visible');
    cy.get('[data-testid="lov-type"]').should('be.visible');
    cy.get('#lov-dropdown-searchable').should('be.visible');
    cy.get('#filtering-function-dropdown-searchable').scrollIntoView().should('be.visible');
    cy.get('[data-testid="dependent-columns"]').scrollIntoView().should('be.visible');
    cy.get('#lov-dropdown-searchable').scrollIntoView().should('be.visible');
    cy.get('#validation-rule-dropdown-searchable').scrollIntoView().should('be.visible');
  });
});
