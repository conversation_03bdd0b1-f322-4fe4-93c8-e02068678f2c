import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import { Avatar, Button, Input } from '@sapiens/insuredpro-ui-component-library';
import { EditTableColumnDef, useTranslation } from '@sapiens/workbench-feature-library';
import { useState } from 'react';

import { Workpads } from '@/models/workpads.model';

interface UseWorkpadColumnsProps {
  isCreatingNewWorkpad?: boolean;
  onSaveNewWorkpad?: (data: { workpadType: number; workpadTitle: string }) => void;
  onCancelNewWorkpad?: () => void;
}

export const useWorkpadColumns = (props?: UseWorkpadColumnsProps) => {
  const { t } = useTranslation('fsw');
  const { isCreatingNewWorkpad = false, onSaveNewWorkpad, onCancelNewWorkpad } = props || {};
  const [editingData, setEditingData] = useState<{ workpadType?: number; workpadTitle?: string }>({});

  const handleDuplicateClick = (workpadNo: number) => {
    console.log(`Duplicate clicked for workpadNo: ${workpadNo}`);
  };

  const handleInputChange = (field: 'workpadType' | 'workpadTitle', value: any) => {
    setEditingData(prev => ({ ...prev, [field]: value }));
  };

  const handleSaveClick = () => {
    if (onSaveNewWorkpad && editingData.workpadType && editingData.workpadTitle) {
      onSaveNewWorkpad({
        workpadType: editingData.workpadType,
        workpadTitle: editingData.workpadTitle
      });
      setEditingData({});
    }
  };

  const handleCancelClick = () => {
    if (onCancelNewWorkpad) {
      onCancelNewWorkpad();
      setEditingData({});
    }
  };

  const isNewRow = (row: any) => row.original.workpadNo === 0;

  const columns: EditTableColumnDef<Workpads>[] = [
    {
      accessorKey: 'workpadNo',
      id: 'workpadNo',
      header: t('workpad.workpadNo') ?? 'WorkpadNo',
      cell: ({ row }) => {
        if (isNewRow(row)) {
          return <span style={{ color: '#999', fontStyle: 'italic' }}>New</span>;
        }
        return row.original.workpadNo;
      }
    },
    {
      accessorKey: 'workpadType',
      id: 'type',
      header: t('workpad.workpadType') ?? 'Workpad types',
      cell: ({ row }) => {
        if (isNewRow(row)) {
          return (
            <Input
              size="small"
              type="number"
              value={editingData.workpadType || ''}
              onChange={(e) => handleInputChange('workpadType', parseInt(e.target.value) || 1)}
              placeholder="Enter type"
              fullWidth
              inputProps={{ min: 1 }}
            />
          );
        }
        return row.original.workpadType;
      }
    },
    {
      accessorKey: 'workpadTitle',
      id: 'title',
      header: t('workpad.workpadTitle') ?? 'Workpad title',
      cell: ({ row }) => {
        if (isNewRow(row)) {
          return (
            <Input
              size="small"
              value={editingData.workpadTitle || ''}
              onChange={(e) => handleInputChange('workpadTitle', e.target.value)}
              placeholder="Enter workpad title"
              fullWidth
            />
          );
        }
        return row.original.workpadTitle;
      }
    },
    {
      accessorKey: 'postDate',
      id: 'postDate',
      header: t('workpad.postDate') ?? 'Post date',
      cell: ({ row }) => {
        if (isNewRow(row)) {
          return <span style={{ color: '#999', fontStyle: 'italic' }}>-</span>;
        }
        return row.original.closeDate;
      }
    },
    {
      accessorKey: 'workpadStatus',
      id: 'status',
      header: t('workpad.workpadStatus') ?? 'Workpad status',
      cell: ({ row }) => {
        if (isNewRow(row)) {
          return <span style={{ color: '#999', fontStyle: 'italic' }}>New</span>;
        }
        return row.original.status;
      }
    },
    {
      accessorKey: 'changedBy',
      id: 'changedBy',
      header: t('workpad.changedBy') ?? 'Changed by',
      cell: ({ row }) => {
        if (isNewRow(row)) {
          return <span style={{ color: '#999', fontStyle: 'italic' }}>-</span>;
        }
        return (
          <Avatar color="tertiary" size="md">
            {row.original.changedBy}
          </Avatar>
        );
      }
    },
    {
      accessorKey: 'actions',
      id: 'actions',
      header: '',
      cell: ({ row }) => {
        if (isNewRow(row)) {
          return (
            <div style={{ display: 'flex', gap: '8px' }}>
              <Button
                variant="text"
                onClick={handleSaveClick}
                disabled={!editingData.workpadTitle || !editingData.workpadType}
                title="Save workpad"
              >
                <CheckIcon />
              </Button>
              <Button
                variant="text"
                onClick={handleCancelClick}
                title="Cancel"
              >
                <CloseIcon />
              </Button>
            </div>
          );
        }
        return (
          <Button variant="text" onClick={() => handleDuplicateClick(row.original.workpadNo)}>
            <ContentCopyIcon />
          </Button>
        );
      }
    }
  ];

  return columns;
};
